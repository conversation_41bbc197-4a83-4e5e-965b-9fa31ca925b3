<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2023 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>jakarta.activation</groupId>
    <artifactId>jakarta.activation-api</artifactId>
    <version>2.1.2</version>
    <packaging>jar</packaging>
    <name>Jakarta Activation API</name>
    <description>${project.name} ${spec.version} Specification</description>
    <url>https://github.com/jakartaee/jaf-api</url>

    <properties>
        <spec.version>2.1</spec.version>
        <spec.title>Jakarta Activation Specification</spec.title>
        <activation.recompile.skip>false</activation.recompile.skip>
        <!-- for the osgiversion-maven-plugin -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javadoc.title>Jakarta Activation API documentation</javadoc.title>
        <legal.doc.source>${project.basedir}/..</legal.doc.source>
        <copyright.exclude>${project.basedir}/../etc/copyright-exclude</copyright.exclude>
        <copyright.ignoreyear>false</copyright.ignoreyear>
        <copyright.scmonly>true</copyright.scmonly>
        <copyright.update>false</copyright.update>
        <spotbugs.skip>false</spotbugs.skip>
        <spotbugs.threshold>Low</spotbugs.threshold>
        <spotbugs.exclude>${project.basedir}/../etc/spotbugs-exclude.xml</spotbugs.exclude>
        <!--Maven plugins version-->
        <spotbugs.version>*******</spotbugs.version>
    </properties>

    <scm>
        <connection>scm:git:ssh://**************/jakartaee/jaf-api.git</connection>
        <developerConnection>scm:git:ssh://**************/jakartaee/jaf-api.git</developerConnection>
        <url>https://github.com/jakartaee/jaf-api</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/jakartaee/jaf-api/issues/</url>
    </issueManagement>

    <licenses>
        <license>
            <name>EDL 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>shannon</id>
            <name>Bill Shannon</name>
            <email><EMAIL></email>
            <organization>Oracle</organization>
            <roles>
                <role>lead</role>
            </roles>
        </developer>
    </developers>

    <!-- following to enable use of "mvn site:stage" -->
    <distributionManagement>
        <site>
            <id>oracle.com</id>
            <url>file:/tmp</url> <!-- not used -->
        </site>
    </distributionManagement>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>META-INF/*.default</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>META-INF/*.default</include>
                </includes>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.8</version>
                    <configuration>
                        <instructions>
                            <_noextraheaders>true</_noextraheaders>
                        </instructions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs.version}</version>
                    <configuration>
                        <skip>${spotbugs.skip}</skip>
                        <threshold>${spotbugs.threshold}</threshold>
                        <excludeFilterFile>${spotbugs.exclude}</excludeFilterFile>
                        <fork>true</fork>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.12.1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!--
            Make sure we're using the correct version of maven.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-version</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.6.3,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[11,)</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--
            Use the JDK 9+ compiler but with -source 1.8 for all
            but the module-info.java file.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>9</release>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                    <showDeprecation>true</showDeprecation>
                    <createMissingPackageInfoClass>false</createMissingPackageInfoClass>
                </configuration>
                <executions>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>8</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                        <configuration>
                            <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                            <shortRevisionLength>7</shortRevisionLength>
                            <revisionOnScmFailure>false</revisionOnScmFailure>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                    <niceManifest>true</niceManifest>
                    <instructions>
                        <_noextraheaders>true</_noextraheaders>
                        <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
                        <Specification-Title>${spec.title}</Specification-Title>
                        <Specification-Version>${spec.version}</Specification-Version>
                        <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                        <Extension-Name>${project.groupId}</Extension-Name>
                        <Implementation-Title>${project.name}</Implementation-Title>
                        <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                        <Implementation-Build-Id>${buildNumber}</Implementation-Build-Id>
                        <DynamicImport-Package>*</DynamicImport-Package>
                        <DynamicImport-Package>org.glassfish.hk2.osgiresourcelocator</DynamicImport-Package>
                        <Import-Package>
                            !org.glassfish.hk2.osgiresourcelocator,
                            *
                        </Import-Package>
                        <!-- optional to allow usage with hk2 resource locator as a fallback -->
                        <Require-Capability><![CDATA[
                          osgi.extender;filter:="(&(osgi.extender=osgi.serviceloader.processor)
                            (version>=1.0.0)(!(version>=2.0.0)))";resolution:=optional,
                          osgi.serviceloader;
                            filter:="(osgi.serviceloader=jakarta.activation.spi.MailcapRegistryProvider)";
                              osgi.serviceloader="jakarta.activation.spi.MailcapRegistryProvider";
                              cardinality:=multiple;resolution:=optional,
                          osgi.serviceloader;
                            filter:="(osgi.serviceloader=jakarta.activation.spi.MimeTypeRegistryProvider)";
                              osgi.serviceloader="jakarta.activation.spi.MimeTypeRegistryProvider";
                              cardinality:=multiple;resolution:=optional,
                          osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=1.8))
                          ]]>
                        </Require-Capability>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <!--
            Tell the source plugin about the sources that may have
            been downloaded by the maven-dependency-plugin.
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>currentyear-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <name>current.year</name>
                            <locale>en,US</locale>
                            <pattern>yyyy</pattern>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${legal.doc.source}</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                    <author>false</author>
                    <description>${javadoc.title}</description>
                    <doctitle>${javadoc.title}</doctitle>
                    <windowtitle>${javadoc.title}</windowtitle>
                    <splitindex>true</splitindex>
                    <use>true</use>
                    <notimestamp>true</notimestamp>
                    <serialwarn>true</serialwarn>
                    <header><![CDATA[<br>Jakarta Activation API v${project.version}]]></header>
                    <bottom>
                        <![CDATA[
Comments to : <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
Copyright &#169; 2019, ${current.year} Eclipse Foundation. All rights reserved.<br>
Use is subject to <a href="{@docRoot}/doc-files/speclicense.html" target="_top">license terms</a>.]]>
                    </bottom>
                    <!-- force the doc-files directory to be copied -->
                    <docfilessubdirs>true</docfilessubdirs>
                    <detectJavaApiLink>false</detectJavaApiLink>
                    <release>11</release>
                    <quiet>true</quiet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <excludeFile>${copyright.exclude}</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>${copyright.scmonly}</scmOnly>
                    <!-- for use with repair -->
                    <update>${copyright.update}</update>
                    <!-- check that year is correct -->
                    <ignoreYear>${copyright.ignoreyear}</ignoreYear>
                    <quiet>false</quiet>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
