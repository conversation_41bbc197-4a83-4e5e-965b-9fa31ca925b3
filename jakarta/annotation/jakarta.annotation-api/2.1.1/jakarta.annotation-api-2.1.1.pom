<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2012, 2022 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.7</version>
        <relativePath/>
    </parent>

    <groupId>jakarta.annotation</groupId>
    <artifactId>jakarta.annotation-api</artifactId>
    <version>2.1.1</version>

    <name>Jakarta Annotations API</name>
    <description>Jakarta Annotations API</description>

    <url>https://projects.eclipse.org/projects/ee4j.ca</url>

    <inceptionYear>2004</inceptionYear>

    <developers>
        <developer>
            <name>Linda De Michiel</name>
            <organization>Oracle Corp.</organization>
        </developer>
        <developer>
            <name>Dmitry Kornilov</name>
            <organization>Oracle Corp.</organization>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:https://github.com/eclipse-ee4j/common-annotations-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/common-annotations-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/common-annotations-api</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/common-annotations-api/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Jakarta Annotations mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/ca-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/ca-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/ca-dev</archive>
        </mailingList>
    </mailingLists>

    <properties>
        <copyright.ignoreyear>false</copyright.ignoreyear>
        <copyright.scmonly>true</copyright.scmonly>
        <copyright.update>false</copyright.update>
        <spotbugs.skip>false</spotbugs.skip>
        <spotbugs.threshold>Low</spotbugs.threshold>
        <spotbugs.version>4.0.4</spotbugs.version>

        <non.final>false</non.final>
        <spec.version>2.1</spec.version>
        <extension.name>jakarta.annotation</extension.name>
        <vendor.name>Eclipse Foundation</vendor.name>
        <implementation.vendor.id>org.glassfish</implementation.vendor.id>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>spec-version-maven-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.1</version>
                    <configuration>
                        <instructions>
                            <_noextraheaders>true</_noextraheaders>
                        </instructions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M3</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[11,)</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[3.6.0,)</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <!-- skip files not under SCM-->
                    <scmOnly>${copyright.scmonly}</scmOnly>
                    <!-- for use with repair -->
                    <update>${copyright.update}</update>
                    <!-- check that year is correct -->
                    <ignoreYear>${copyright.ignoreyear}</ignoreYear>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${basedir}/..</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>9</release>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                </configuration>
                <executions>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>8</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>spec-version-maven-plugin</artifactId>
                <configuration>
                    <specMode>jakarta</specMode>
                    <spec>
                        <nonFinal>${non.final}</nonFinal>
                        <jarType>api</jarType>
                        <specBuild>${spec.build}</specBuild>
                        <specVersion>${spec.version}</specVersion>
                        <newSpecVersion>${new.spec.version}</newSpecVersion>
                        <specImplVersion>${project.version}</specImplVersion>
                        <apiPackage>${extension.name}</apiPackage>
                    </spec>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>set-spec-properties</goal>
                            <!-- TODO:
                            glassfish-spec-version-maven-plugin needs to be updated
                            in order to check 'jakarta.' prefixed values in manifest entries
                            -->
                            <!--<goal>check-module</goal>-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Bundle-Version>${spec.bundle.version}</Bundle-Version>
                        <Bundle-SymbolicName>${spec.bundle.symbolic-name}</Bundle-SymbolicName>
                        <Extension-Name>${spec.extension.name}</Extension-Name>
                        <Implementation-Version>${spec.implementation.version}</Implementation-Version>
                        <Specification-Version>${spec.specification.version}</Specification-Version>
                        <Bundle-Description>${project.name}</Bundle-Description>
                        <Specification-Vendor>${vendor.name}</Specification-Vendor>
                        <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                        <Implementation-Vendor-Id>${implementation.vendor.id}</Implementation-Vendor-Id>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Build-Id>${buildNumber}</Implementation-Build-Id>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                    <release>11</release>
                    <additionalOptions>--add-modules java.sql</additionalOptions>
                    <notimestamp>true</notimestamp>
                    <nosince>true</nosince>
                    <docfilessubdirs>true</docfilessubdirs>
                    <doctitle>Jakarta Annotations ${project.version} API Specification</doctitle>
                    <header><![CDATA[<br>Jakarta Annotations API v${project.version}]]></header>
                    <bottom><![CDATA[
                    Copyright &#169; 2019, 2021 Eclipse Foundation. All rights reserved.<br>
                    Use is subject to <a href="{@docRoot}/doc-files/EFSL.html" target="_top">license terms</a>.]]>
                    </bottom>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <skip>${spotbugs.skip}</skip>
                    <threshold>${spotbugs.threshold}</threshold>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
