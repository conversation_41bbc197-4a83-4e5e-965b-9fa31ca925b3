<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2020 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>jakarta.platform</groupId>
        <artifactId>jakartaee-api-parent</artifactId>
        <version>9.1.0</version>
    </parent>
    <artifactId>jakarta.jakartaee-bom</artifactId>
    <packaging>pom</packaging>
    <name>Jakarta EE API BOM</name>
    <description>Jakarta EE API BOM</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>glassfishbuild-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-pom</id>
                        <goals>
                            <goal>generate-pom</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>attach-all-artifacts</id>
                        <goals>
                            <goal>attach-all-artifacts</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <!-- Web Profile -->
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet.jsp</groupId>
                <artifactId>jakarta.servlet.jsp-api</artifactId>
                <version>${jakarta.servlet.jsp-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${jakarta.el-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet.jsp.jstl</groupId>
                <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
                <version>${jakarta.servlet.jsp.jstl-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.faces</groupId>
                <artifactId>jakarta.faces-api</artifactId>
                <version>${jakarta.faces-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jakarta.ws.rs-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.websocket</groupId>
                <artifactId>jakarta.websocket-api</artifactId>
                <version>${jakarta.websocket-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>${jakarta.json-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json.bind</groupId>
                <artifactId>jakarta.json.bind-api</artifactId>
                <version>${jakarta.json.bind-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ejb</groupId>
                <artifactId>jakarta.ejb-api</artifactId>
                <version>${jakarta.ejb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.transaction</groupId>
                <artifactId>jakarta.transaction-api</artifactId>
                <version>${jakarta.transaction-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>${jakarta.persistence-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.interceptor</groupId>
                <artifactId>jakarta.interceptor-api</artifactId>
                <version>${jakarta.interceptor-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.enterprise</groupId>
                <artifactId>jakarta.enterprise.cdi-api</artifactId>
                <version>${jakarta.enterprise.cdi-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.inject</groupId>
                <artifactId>jakarta.inject-api</artifactId>
                <version>${jakarta.inject.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.authentication</groupId>
                <artifactId>jakarta.authentication-api</artifactId>
                <version>${jakarta.authentication-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.security.enterprise</groupId>
                <artifactId>jakarta.security.enterprise-api</artifactId>
                <version>${jakarta.security.enterprise-api.version}</version>
            </dependency>

            <!-- Full Platform -->
            <dependency>
                <groupId>jakarta.jms</groupId>
                <artifactId>jakarta.jms-api</artifactId>
                <version>${jakarta.jms-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${jakarta.activation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>${jakarta.mail-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${jakarta.mail-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.resource</groupId>
                <artifactId>jakarta.resource-api</artifactId>
                <version>${jakarta.resource-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.authorization</groupId>
                <artifactId>jakarta.authorization-api</artifactId>
                <version>${jakarta.authorization-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.enterprise.concurrent</groupId>
                <artifactId>jakarta.enterprise.concurrent-api</artifactId>
                <version>${jakarta.enterprise.concurrent-api.version}</version>
            </dependency>
             <dependency>
                <groupId>jakarta.batch</groupId>
                <artifactId>jakarta.batch-api</artifactId>
                <version>${jakarta.batch-api.version}</version>
            </dependency>

            <!-- Optional APIs -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.ws</groupId>
                <artifactId>jakarta.xml.ws-api</artifactId>
                <version>${jakarta.xml.ws-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.jws</groupId>
                <artifactId>jakarta.jws-api</artifactId>
                <version>${jakarta.jws-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.soap</groupId>
                <artifactId>jakarta.xml.soap-api</artifactId>
                <version>${jakarta.xml.soap-api.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
