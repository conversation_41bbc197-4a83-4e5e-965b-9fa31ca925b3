<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.guava</groupId>
  <artifactId>guava-parent</artifactId>
  <version>28.1-android</version>
  <packaging>pom</packaging>
  <name>Guava Maven Parent</name>
  <description>Parent for guava artifacts</description>
  <url>https://github.com/google/guava</url>
  <properties>
    <!-- Override this with -Dtest.include="**/SomeTest.java" on the CLI -->
    <test.include>%regex[.*.class]</test.include>
    <truth.version>0.44</truth.version>
    <animal.sniffer.version>1.18</animal.sniffer.version>
    <maven-javadoc-plugin.version>3.1.0</maven-javadoc-plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/google/guava/issues</url>
  </issueManagement>
  <inceptionYear>2010</inceptionYear>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:https://github.com/google/guava.git</connection>
    <developerConnection>scm:git:**************:google/guava.git</developerConnection>
    <url>https://github.com/google/guava</url>
  </scm>
  <developers>
    <developer>
      <id>kevinb9n</id>
      <name>Kevin Bourrillion</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <organizationUrl>http://www.google.com</organizationUrl>
      <roles>
        <role>owner</role>
        <role>developer</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
  </developers>
  <ciManagement>
    <system>Travis CI</system>
    <url>https://travis-ci.org/google/guava</url>
  </ciManagement>
  <modules>
    <module>guava</module>
    <module>guava-bom</module>
    <module>guava-testlib</module>
    <module>guava-tests</module>
  </modules>
  <build>
    <!-- Handle where Guava deviates from Maven defaults -->
    <sourceDirectory>src</sourceDirectory>
    <testSourceDirectory>test</testSourceDirectory>
    <resources>
      <resource>
        <directory>src</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>test</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-versions</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0.5</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>1.8.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.0.2</version>
          <configuration>
            <excludes>
              <exclude>**/ForceGuavaCompilation*</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <phase>post-integration-test</phase>
              <goals><goal>jar</goal></goals>
            </execution>
          </executions>
          <configuration>
            <excludes>
              <exclude>**/ForceGuavaCompilation*</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>${animal.sniffer.version}</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java16-sun</artifactId>
              <version>1.10</version>
            </signature>
            <ignores>
              <!-- Unsafe isn't part of the documented Java 6 API, but it is available.
                   And in cases where it's not, we have fallbacks. -->
              <ignore>sun.misc.Unsafe</ignore>
            </ignores>
          </configuration>
          <executions>
            <execution>
              <id>check-java-version-compatibility</id>
              <phase>test</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
          <configuration>
            <quiet>true</quiet>
            <notimestamp>true</notimestamp>
            <encoding>UTF-8</encoding>
            <docencoding>UTF-8</docencoding>
            <charset>UTF-8</charset>
            <additionalOptions>
              <additionalOption>-XDignore.symbol.file</additionalOption>
              <additionalOption>-Xdoclint:-html</additionalOption>
            </additionalOptions>
            <linksource>true</linksource>
            <source>8</source>
          </configuration>
          <executions>
            <execution>
              <id>attach-docs</id>
              <phase>post-integration-test</phase>
              <goals><goal>jar</goal></goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.7.2</version>
          <configuration>
            <includes>
              <include>${test.include}</include>
            </includes>
            <!-- By having our own entries here, we also override the default exclusion filter, which excludes all nested classes. -->
            <excludes>
              <!-- https://github.com/google/guava/issues/2840 -->
              <exclude>%regex[.*PackageSanityTests.*.class]</exclude>
              <!-- FeatureUtilTest.*ExampleDerivedInterfaceTester, com.google.common.io.*Tester, incidentally FeatureSpecificTestSuiteBuilderTest.MyAbstractTester (but we don't care either way because it's not meant to run on its own but works OK if it does)... but not NullPointerTesterTest, etc. -->
              <exclude>%regex[.*Tester.class]</exclude>
              <!-- Anonymous TestCase subclasses in GeneratedMonitorTest -->
              <exclude>%regex[.*[$]\d+.class]</exclude>
            </excludes>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <runOrder>alphabetical</runOrder>
            <!-- Set max heap for tests. -->
            <!-- Catch dependencies on the default locale by setting it to hi-IN. -->
            <argLine>-Xmx1536M -Duser.language=hi -Duser.country=IN</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.0.0-M2</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <site>
      <id>guava-site</id>
      <name>Guava Documentation Site</name>
      <url>scp://dummy.server/dontinstall/usestaging</url>
    </site>
  </distributionManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.checkerframework</groupId>
        <artifactId>checker-compat-qual</artifactId>
        <version>2.5.5</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>2.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.j2objc</groupId>
        <artifactId>j2objc-annotations</artifactId>
        <version>1.3</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.12</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>3.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>2.19.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.jimfs</groupId>
        <artifactId>jimfs</artifactId>
        <version>1.1</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.truth</groupId>
        <artifactId>truth</artifactId>
        <version>${truth.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <!-- use the guava we're building. -->
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.caliper</groupId>
        <artifactId>caliper</artifactId>
        <version>1.0-beta-2</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <!-- use the guava we're building. -->
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
        <id>sonatype-oss-release</id>
        <build>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-source-plugin</artifactId>
              <version>2.1.2</version>
              <executions>
                <execution>
                  <id>attach-sources</id>
                  <goals>
                    <goal>jar-no-fork</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
              <version>${maven-javadoc-plugin.version}</version>
                <executions>
                  <execution>
                    <id>attach-javadocs</id>
                    <goals>
                      <goal>jar</goal>
                    </goals>
                </execution>
              </executions>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>1.6</version>
              <executions>
                <execution>
                  <id>sign-artifacts</id>
                  <phase>verify</phase>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
      </build>
    </profile>
  </profiles>
</project>
