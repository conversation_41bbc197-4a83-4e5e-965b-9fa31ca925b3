<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>
    <groupId>net.sf.jopt-simple</groupId>
    <artifactId>jopt-simple</artifactId>
    <version>5.0.4</version>
    <packaging>jar</packaging>
    <name>JOpt Simple</name>
    <url>http://jopt-simple.github.io/jopt-simple</url>
    <description>A Java library for parsing command line options</description>
    <licenses>
        <license>
            <name>The MIT License</name>
            <url>http://www.opensource.org/licenses/mit-license.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:git://github.com/jopt-simple/jopt-simple.git</connection>
        <developerConnection>scm:git:ssh://**************/jopt-simple/jopt-simple.git</developerConnection>
        <url>https://github.com/jopt-simple/jopt-simple</url>
    </scm>
    <issueManagement>
        <system>GitHub</system>
        <url>https://github.com/jopt-simple/jopt-simple/issues</url>
    </issueManagement>
    <developers>
        <developer>
            <id>pholser</id>
            <name>Paul Holser</name>
            <email><EMAIL></email>
            <url>http://www.pholser.com</url>
        </developer>
    </developers>
    <properties>
        <project.build.outputEncoding>UTF-8</project.build.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.project.outputEncoding>UTF-8</project.project.outputEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.8.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.infinitest</groupId>
            <artifactId>continuous-testing-toolkit</artifactId>
            <version>1.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.scm</groupId>
                <artifactId>maven-scm-provider-gitexe</artifactId>
                <version>1.9.2</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.scm</groupId>
                <artifactId>maven-scm-manager-plexus</artifactId>
                <version>1.9.2</version>
            </extension>
            <extension>
                <groupId>org.kathrynhuxtable.maven.wagon</groupId>
                <artifactId>wagon-gitsite</artifactId>
                <version>0.3.1</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-maven-plugin</artifactId>
                <version>1.13</version>
                <configuration>
                    <signature>
                        <groupId>org.codehaus.mojo.signature</groupId>
                        <artifactId>java17</artifactId>
                        <version>1.0</version>
                    </signature>
                </configuration>
                <executions>
                    <execution>
                        <id>check-signature</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                    <!-- Needs updated version of animal sniffer plugin
                    <execution>
                        <id>check-test-signature</id>
                        <goals>
                            <goal>test-check</goal>
                        </goals>
                    </execution>
                    -->
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.3.7</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
                        <Bundle-Name>${project.artifactId}</Bundle-Name>
                        <Bundle-Version>${project.version}</Bundle-Version>
                        <Bundle-Vendor>${project.name}</Bundle-Vendor>
                        <Export-Package>joptsimple,joptsimple.util</Export-Package>
                        <Private-Package>joptsimple.internal</Private-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>bundle</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <check>
                        <haltOnFailure>true</haltOnFailure>
                        <totalBranchRate>96</totalBranchRate>
                        <totalLineRate>99</totalLineRate>
                    </check>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.4</version>
                <configuration>
                    <minimumTokens>40</minimumTokens>
                    <targetJdk>1.7</targetJdk>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>cpd-check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>paste-examples</id>
                        <phase>post-site</phase>
                        <configuration>
                            <target>
                                <property name="java.io.tmpdir" value="${java.io.tmpdir}" />
                                <property name="example.src.dir" value="src/test/java" />
                                <property name="example.class.path" refid="maven.test.classpath" />
                                <property name="website.staging.dir" value="${project.build.directory}/site" />
                                <ant antfile="paste-examples.xml" target="-paste-examples" />
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <outputEncoding>UTF-8</outputEncoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>1.1.9</version>
                <configuration>
                    <targetClasses>
                        <param>joptsimple*</param>
                    </targetClasses>
                    <targetTests>
                        <param>*Test</param>
                    </targetTests>
                    <excludedMethods>
                        <param>equals</param>
                        <param>hashCode</param>
                    </excludedMethods>
                    <excludedClasses>
                        <param>joptsimple.examples*</param>
                        <param>*EqualsHash*Test</param>
                        <param>*TestCase</param>
                        <param>*Fixture</param>
                        <param>*Harness</param>
                        <param>*Matchers</param>
                        <param>*Ctor</param>
                        <param>*Problematic</param>
                        <param>*ValueOfHaver</param>
                    </excludedClasses>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                       <id>attach-sources</id>
                       <phase>package</phase>
                       <goals>
                         <goal>jar</goal>
                       </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.8</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <configuration>
                    <source>1.7</source>
                    <show>public</show>
                    <excludePackageNames>joptsimple.examples:joptsimple.internal:joptsimple.internal.*</excludePackageNames>
                    <links>
                        <link>https://docs.oracle.com/javase/8/docs/api</link>
                    </links>
                    <bottom><![CDATA[<i>&copy; Copyright 2004-2015 Paul R. Holser, Jr.  All rights reserved. Licensed under The MIT License. <EMAIL></i>]]></bottom>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <check>
                        <haltOnFailure>true</haltOnFailure>
                        <totalBranchRate>97</totalBranchRate>
                        <totalLineRate>99</totalLineRate>
                    </check>
                    <formats>
                        <format>xml</format>
                        <format>html</format>
                    </formats>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <threshold>Low</threshold>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.4</version>
                <configuration>
                    <minimumTokens>40</minimumTokens>
                    <targetJdk>1.7</targetJdk>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
    <distributionManagement>
        <site>
            <id>github-project-site</id>
            <url>gitsite:**************/jopt-simple/jopt-simple.git</url>
        </site>
    </distributionManagement>
</project>
