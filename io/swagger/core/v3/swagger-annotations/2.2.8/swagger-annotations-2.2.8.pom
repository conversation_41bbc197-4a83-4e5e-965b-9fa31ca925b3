<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.swagger.core.v3</groupId>
        <artifactId>swagger-project</artifactId>
        <version>2.2.8</version>
        <relativePath>../..</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>swagger-annotations</artifactId>
    <name>swagger-annotations</name>
    <description>swagger-annotations</description>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Automatic-Module-Name>io.swagger.v3.oas.annotations</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-eclipse-transformer-maven-plugin</artifactId>
                <version>${project.version}</version>
                <configuration>
                    <attach>false</attach>
                    <classifier>jakarta</classifier>
                    <outputDirectory>${project.basedir}/../swagger-project-jakarta/modules/${project.artifactId}-jakarta/transformed</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>transform-jakarta</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <!-- this is used during release (see CI/CI.md file for details) to prepare javadocs for publishing to gh-pages -->
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${enforcer-plugin-version}</version>
                        <executions>
                            <execution>
                                <id>enforce-no-snapshots</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireReleaseVersion>
                                            <message>Publish javadocs for a SNAPSHOT is not allowed.</message>
                                        </requireReleaseVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>copy-resources</id>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/javadocprep/swagger-core/${project.version}/apidocs</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>${project.build.directory}/apidocs</directory>
                                            <filtering>true</filtering>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <dependencies>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
