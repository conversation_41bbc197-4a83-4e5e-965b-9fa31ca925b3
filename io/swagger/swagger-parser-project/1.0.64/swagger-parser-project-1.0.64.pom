<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>io.swagger</groupId>
    <artifactId>swagger-parser-project</artifactId>
    <version>1.0.64</version>
    <packaging>pom</packaging>
    <name>swagger-parser-project</name>
    <developers>
        <developer>
            <id>fehguy</id>
            <name><PERSON></name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>webron</id>
            <name><PERSON></name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>fge</id>
            <name><PERSON> Galiegue</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <issueManagement>
        <system>github</system>
        <url>https://github.com/swagger-api/swagger-parser/issues</url>
    </issueManagement>
    <mailingLists>
        <mailingList>
            <name>swagger-swaggersocket</name>
            <archive>https://groups.google.com/forum/#!forum/swagger-swaggersocket</archive>
        </mailingList>
    </mailingLists>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:**************:swagger-api/swagger-parser.git</connection>
        <developerConnection>scm:git:**************:swagger-api/swagger-parser.git</developerConnection>
        <url>https://github.com/swagger-api/swagger-parser</url>
    </scm>
    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>
    <build>
        <defaultGoal>install</defaultGoal>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>logback.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire-version}</version>
                <configuration>
                    <testNGArtifactName>none:none</testNGArtifactName>
                </configuration>
                <executions>
                    <execution>
                        <id>test-testng</id>
                        <phase>test</phase>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <junitArtifactName>none:none</junitArtifactName>
                            <testNGArtifactName>org.testng:testng</testNGArtifactName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <excludes>
                        <exclude>**/logback.xml</exclude>
                    </excludes>
                    <archive>
                        <manifestEntries>
                            <mode>development</mode>
                            <url>${project.url}</url>
                            <implementation-version>${project.version}</implementation-version>
                            <package>io.swagger</package>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <source>1.7</source>
                    <encoding>UTF-8</encoding>
                    <maxmemory>1g</maxmemory>
                    <links>
                        <link>http://java.sun.com/javase/6/docs/api/</link>
                    </links>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.5.201505241946</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration></configuration>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <rule>
                            <element>BUNDLE</element>
                        </rule>
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M2</version>
                <executions>
                    <execution>
                        <id>enforce-no-snapshots</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireReleaseDeps>
                                    <message>No Snapshots Allowed!</message>
                                    <onlyWhenRelease>true</onlyWhenRelease>
                                </requireReleaseDeps>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.8</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                    <stagingProgressTimeoutMinutes>30</stagingProgressTimeoutMinutes>
                    <!--                    <skipStaging>true</skipStaging>
                                        <skipRemoteStaging>true</skipRemoteStaging>
                                        <skipNexusStagingDeployMojo>true</skipNexusStagingDeployMojo>-->
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                    <configuration>
                        <!-- Prevent `gpg` from using pinentry programs -->
                        <gpgArguments>
                            <arg>--pinentry-mode</arg>
                            <arg>loopback</arg>
                        </gpgArguments>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <profile>
            <id>security</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.owasp</groupId>
                        <artifactId>dependency-check-maven</artifactId>
                        <version>6.5.3</version>
                        <configuration>
                            <skipSystemScope>true</skipSystemScope>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <configuration>
                            <!-- Prevent `gpg` from using pinentry programs -->
                            <gpgArguments>
                                <arg>--pinentry-mode</arg>
                                <arg>loopback</arg>
                            </gpgArguments>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-core</artifactId>
                <version>${swagger-core-version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger-core-version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml-version}</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng-version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>snakeyaml</artifactId>
                        <groupId>org.yaml</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.tomakehurst</groupId>
                <artifactId>wiremock</artifactId>
                <version>${wiremock-version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>modules/swagger-parser</module>
        <module>modules/swagger-compat-spec-parser</module>
    </modules>
    <repositories>
        <repository>
            <id>sonatype-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>sonatype-releases</id>
            <url>https://oss.sonatype.org/content/repositories/releases</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <properties>
        <commons-io-version>2.11.0</commons-io-version>
        <snakeyaml-version>1.33</snakeyaml-version>
        <slf4j-version>1.7.28</slf4j-version>
        <swagger-core-version>1.6.9</swagger-core-version>
        <junit-version>4.13.1</junit-version>
        <testng-version>6.9.6</testng-version>
        <jmockit-version>1.19</jmockit-version>
        <wiremock-version>2.25.0</wiremock-version>
        <surefire-version>2.18.1</surefire-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sonatypeOssDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/snapshots/</sonatypeOssDistMgmtSnapshotsUrl>
    </properties>
</project>
