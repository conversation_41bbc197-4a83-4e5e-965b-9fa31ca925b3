<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.codelibs</groupId>
	<artifactId>jhighlight</artifactId>
	<version>1.1.0</version>
	<packaging>jar</packaging>
	<name>JHighlight</name>
	<description>
    JHighlight is an embeddable pure Java syntax highlighting
    library that supports Java, HTML, XHTML, XML and LZX
    languages and outputs to XHTML.
    
    It also supports RIFE templates tags and highlights them
    clearly so that you can easily identify the difference
    between your RIFE markup and the actual marked up source.
	</description>
	<inceptionYear>2011</inceptionYear>
	<url>https://github.com/codelibs/jhighlight</url>
	<licenses>
		<license>
			<name>CDDL, v1.0</name>
			<url>http://www.opensource.org/licenses/cddl1.php</url>
			<distribution>repo</distribution>
		</license>
		<license>
			<name>LGPL, v2.1 or later</name>
			<url>http://www.opensource.org/licenses/lgpl-license.php</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<scm>
		<connection>scm:git:**************:codelibs/jhighlight.git</connection>
		<developerConnection>scm:git:**************:codelibs/jhighlight.git</developerConnection>
		<url>**************:codelibs/jhighlight.git</url>
	</scm>
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>9</version>
	</parent>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<includes>
						<include>**/*Test.java</include>
					</includes>
					<useSystemClassLoader>false</useSystemClassLoader>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.1.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<docencoding>UTF-8</docencoding>
					<charset>UTF-8</charset>
					<source>8</source>
					<doclint>none</doclint>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.2</version>
				<configuration>
					<archive>
						<manifestEntries>
							<Automatic-Module-Name>org.codelibs.jhighlight</Automatic-Module-Name>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.7</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.3</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>
