<?xml version="1.0" encoding="UTF-8"?>
<!-- Licensed to the Apache Software Foundation (ASF) under one or more contributor 
	license agreements. See the NOTICE file distributed with this work for additional 
	information regarding copyright ownership. The ASF licenses this file to 
	You under the Apache License, Version 2.0 (the "License"); you may not use 
	this file except in compliance with the License. You may obtain a copy of 
	the License at http://www.apache.org/licenses/LICENSE-2.0 Unless required 
	by applicable law or agreed to in writing, software distributed under the 
	License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS 
	OF ANY KIND, either express or implied. See the License for the specific 
	language governing permissions and limitations under the License. -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<artifactId>xmpbox</artifactId>
	<packaging>bundle</packaging>
	<name>Apache XmpBox</name>

	<description>
    The Apache XmpBox library is an open source Java tool that implements Adobe's XMP(TM)
    specification. It can be used to parse, validate and create xmp contents.
    It is mainly used by subproject preflight of Apache PDFBox. 
    XmpBox is a subproject of Apache PDFBox.
  </description>

	<parent>
		<groupId>org.apache.pdfbox</groupId>
		<artifactId>pdfbox-parent</artifactId>
		<version>3.0.4</version>
		<relativePath>../parent/pom.xml</relativePath>
	</parent>

	<dependencies>
	    <dependency>
	      <groupId>org.junit.jupiter</groupId>
	      <artifactId>junit-jupiter</artifactId>
          <version>${junit.version}</version>
	      <scope>test</scope>
	    </dependency>
        <dependency>
		  <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
          <version>${commons-logging.version}</version>
        </dependency>
	</dependencies>

  <scm>
      <connection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/xmpbox</connection>
      <developerConnection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/xmpbox</developerConnection>
      <url>https://svn.apache.org/viewvc/pdfbox/tags/3.0.4/xmpbox</url>
  </scm>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<extensions>true</extensions>
				<configuration>
		          <instructions>
		              <Automatic-Module-Name>org.apache.xmpbox</Automatic-Module-Name>
		          </instructions>
                </configuration>
			</plugin>
		</plugins>
	</build>

</project>
