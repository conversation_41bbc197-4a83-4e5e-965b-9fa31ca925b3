<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox-parent</artifactId>
    <version>3.0.4</version>
    <relativePath>../parent/pom.xml</relativePath>
  </parent>

  <artifactId>fontbox</artifactId>
  <packaging>bundle</packaging>

  <name>Apache FontBox</name>
  <description>
    The Apache FontBox library is an open source Java tool to obtain low level information
    from font files. FontBox is a subproject of Apache PDFBox.
  </description>

  <inceptionYear>2008</inceptionYear>
  <url>http://pdfbox.apache.org/</url>

  <scm>
      <connection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/fontbox</connection>
      <developerConnection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/fontbox</developerConnection>
      <url>https://svn.apache.org/viewvc/pdfbox/tags/3.0.4/fontbox</url>
  </scm>

  <dependencies>
    <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox-io</artifactId>
        <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>${commons-logging.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
            <instructions>
                <Automatic-Module-Name>org.apache.fontbox</Automatic-Module-Name>
            </instructions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
            <excludes>
                <exclude>src/main/resources/org/apache/fontbox/cmap/*</exclude>
                <exclude>src/main/resources/org/apache/fontbox/unicode/*</exclude>
                <exclude>src/test/resources/afm/*</exclude>
            </excludes>
        </configuration>
      </plugin>
      <!-- download test files from JIRA and keep them in repository cache -->
      <plugin>
          <groupId>com.googlecode.maven-download-plugin</groupId>
          <artifactId>download-maven-plugin</artifactId>
          <executions>
              <execution>
                  <id>PDFBOX-4038</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/12684264/SourceSansProBold.otf</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>28a044a2685fbc8da7810d9ac7b6b93a95542d504d7d8e671f009b8ebb2f5b70c974be7ea78974b188d8e6ab17d65b08f276c054927857315d5aad26f6fe36fc</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-3997</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/12896461/NotoEmoji-Regular.ttf</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>51b01ab0794be9f92c59679f6d56d4ce09ed959daeb9ec945bb837eb15a82ab302e83b29aab1972ac9cb648f7196a5f5ff4488a4622b36bedbc9cd0cab6dc3de</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-3379</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/12809395/DejaVuSansMono.ttf</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>1af1ce3e6d34a0b89c93072d8646e92cceb45b276389d2dd0d84457ec1193394d2bcc49bf3ce99c9c6b2658cd1337fc40ee5c61957f74cd45dbc3d51b6aef417</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-5328</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/13036376/NotoSansSC-Regular.otf</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>cbdd317d16099d24736457eef631353c7830a1a3c132b01f2cdc1e6a0c21a78e3b1fe8479b3f40179e7630a15cc23a093775bb22d521dba39376bb367d497b21</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-5356</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://mirror.math.princeton.edu/pub/CTAN/fonts/opensans/type1/OpenSans-Regular.pfb</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>2787fcecc0feb1c9e6ff0d8de6193658413863e44eaab572751ca7e6c3b369c0a9731f4952cb0821f307760f0422f77c5f0d3fe7df6b054643fb39423e8d70ee</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-5713</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/13064282/DejaVuSerifCondensed.pfb</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>6ef13c3497862dc8e4c2a4261bc3a7ef3e2dd75e00ae2af4912b236b387225541db76c72854fbb2323d1064311ffdda9e64ed7065afc3a7d13f5b71b7df2f2ef</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-5728</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://issues.apache.org/jira/secure/attachment/13065025/NotoMono-Regular.ttf</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <sha512>a5f3a12a02d096337cefd82a352a9d4f43555283873211c4ed0ac63eb1e722514dbd97dc959208e38643784b007ef27a96280f57ef01355fdbd8884b84d13d4c</sha512>
                  </configuration>
              </execution>
              <execution>
                  <id>PDFBOX-4106</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>wget</goal>
                  </goals>
                  <configuration>
                      <url>https://moji.or.jp/wp-content/ipafont/IPAfont/ipag00303.zip</url>
                      <outputDirectory>${project.build.directory}/fonts</outputDirectory>
                      <outputFileName>ipag00303.zip</outputFileName>
                      <unpack>true</unpack>
                      <sha512>59535137c649a2f8bdbb463cd716426811a6003a65883ca6e45bb0af1d526b3889af0fba3a353e90bc8d373cd32b90a27ff9ff6916ecbccb42e922c09e9b046a</sha512>
                  </configuration>
              </execution>
          </executions>
      </plugin>
    </plugins>
  </build>

</project>

