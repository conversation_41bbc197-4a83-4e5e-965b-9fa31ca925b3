<?xml version="1.0" encoding="UTF-8"?>

<!--
 ! Licensed to the Apache Software Foundation (ASF) under one or more
 ! contributor license agreements.  See the NOTICE file distributed with
 ! this work for additional information regarding copyright ownership.
 ! The ASF licenses this file to You under the Apache License, Version 2.0
 ! (the "License"); you may not use this file except in compliance with
 ! the License.  You may obtain a copy of the License at
 !
 !      http://www.apache.org/licenses/LICENSE-2.0
 !
 ! Unless required by applicable law or agreed to in writing, software
 ! distributed under the License is distributed on an "AS IS" BASIS,
 ! WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ! See the License for the specific language governing permissions and
 ! limitations under the License.
 !-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox-parent</artifactId>
    <version>3.0.4</version>
    <relativePath>../parent/pom.xml</relativePath>
  </parent>

  <artifactId>pdfbox-tools</artifactId>

  <packaging>jar</packaging>
  <name>Apache PDFBox tools</name>
  <description>
    The Apache PDFBox library is an open source Java tool for working with PDF documents. 
    This artefact contains commandline tools using Apache PDFBox.
  </description>
  <inceptionYear>2002</inceptionYear>

  <scm>
      <connection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/tools</connection>
      <developerConnection>scm:svn:https://svn.apache.org/repos/asf/pdfbox/tags/3.0.4/tools</developerConnection>
      <url>https://svn.apache.org/viewvc/pdfbox/tags/3.0.4/tools</url>
  </scm>

  <dependencies>
    <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>pdfbox-debugger</artifactId>
        <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>${commons-io.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>info.picocli</groupId>
      <artifactId>picocli</artifactId>
      <version>${picocli.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>jbig2-imageio</artifactId>
        <version>${jbig2.version}</version>
        <scope>test</scope>
    </dependency>
    <!-- For legal reasons (incompatible license), these two dependencies 
        are to be used only in the tests and may not be distributed. 
        See also LEGAL-195 -->
    <dependency>
        <groupId>com.github.jai-imageio</groupId>
        <artifactId>jai-imageio-core</artifactId>
        <version>${jai.version}</version>
        <scope>test</scope>
    </dependency>    
    <dependency>
        <groupId>com.github.jai-imageio</groupId>
        <artifactId>jai-imageio-jpeg2000</artifactId>
        <version>${jai.version}</version>
        <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
        <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
                <systemPropertyVariables>
                    <java.util.logging.config.file>src/test/resources/logging.properties</java.util.logging.config.file>
                </systemPropertyVariables>
            </configuration>
        </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
                <archive>
                    <manifestEntries>
                        <Automatic-Module-Name>org.apache.pdfbox.tools</Automatic-Module-Name>
                    </manifestEntries>
                </archive>
            </configuration>
        </plugin>
    </plugins>
  </build>

</project>

