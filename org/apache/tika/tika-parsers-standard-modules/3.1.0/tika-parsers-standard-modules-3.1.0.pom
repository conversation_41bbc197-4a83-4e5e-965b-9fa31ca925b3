<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>tika-parsers-standard</artifactId>
    <groupId>org.apache.tika</groupId>
    <version>3.1.0</version>
    <relativePath>../pom.xml</relativePath>
  </parent>


  <modelVersion>4.0.0</modelVersion>

  <artifactId>tika-parsers-standard-modules</artifactId>
  <packaging>pom</packaging>
  <name>Apache Tika standard parser modules</name>

  <url>https://tika.apache.org/</url>


  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-core</artifactId>
      <version>${project.version}</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <modules>
    <module>tika-parser-jdbc-commons</module>
    <module>tika-parser-digest-commons</module>
    <module>tika-parser-mail-commons</module>
    <module>tika-parser-xmp-commons</module>
    <module>tika-parser-zip-commons</module>

    <module>tika-parser-ocr-module</module>
    <module>tika-parser-audiovideo-module</module>
    <module>tika-parser-code-module</module>
    <module>tika-parser-html-module</module>
    <module>tika-parser-image-module</module>
    <module>tika-parser-text-module</module>
    <module>tika-parser-font-module</module>
    <module>tika-parser-microsoft-module</module>
    <module>tika-parser-pkg-module</module>
    <module>tika-parser-xml-module</module>
    <module>tika-parser-pdf-module</module>
    <module>tika-parser-apple-module</module>
    <module>tika-parser-cad-module</module>
    <module>tika-parser-mail-module</module>
    <module>tika-parser-miscoffice-module</module>
    <module>tika-parser-news-module</module>
    <module>tika-parser-crypto-module</module>
    <module>tika-parser-webarchive-module</module>
  </modules>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${rat.version}</version>
          <configuration>
            <excludes>
              <exclude>src/test/resources/test-documents/**</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <configuration>
            <instructions>
              <Bundle-DocURL>${project.url}</Bundle-DocURL>
              <Import-Package>
                org.apache.tika.*,
                *;resolution:=optional
              </Import-Package>
            </instructions>
          </configuration>
          <executions>
            <execution>
              <id>bundle-manifest</id>
              <phase>process-classes</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <scm>
    <tag>tika-3.1.0-rc1</tag>
  </scm>
</project>