<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>tika-parsers-standard-modules</artifactId>
    <groupId>org.apache.tika</groupId>
    <version>3.1.0</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>tika-parser-microsoft-module</artifactId>
  <name>Apache Tika Microsoft parser module</name>

  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <!-- needed for msg files -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-html-module</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- needed for msg files -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-text-module</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-xml-module</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- needed for msg files -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-mail-commons</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.pff</groupId>
      <artifactId>java-libpst</artifactId>
      <version>${libpst.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-zip-commons</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-scratchpad</artifactId>
      <version>${poi.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>
    <!-- needed by jackcess -->
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
    </dependency>
    <dependency>
      <groupId>com.healthmarketscience.jackcess</groupId>
      <artifactId>jackcess</artifactId>
    </dependency>
    <dependency>
      <groupId>com.healthmarketscience.jackcess</groupId>
      <artifactId>jackcess-encrypt</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcjmail-jdk18on</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
    </dependency>
    <!-- logging -->
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j2-impl</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- needed for libpst test files -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>tika-parser-mail-module</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>org.apache.tika.parser.microsoft</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <scm>
    <tag>tika-3.1.0-rc1</tag>
  </scm>
</project>