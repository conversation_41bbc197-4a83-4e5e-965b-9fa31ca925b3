<?xml version="1.0" encoding="ISO-8859-1"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>21</version>
  </parent>
  <groupId>org.apache.commons</groupId>
  <artifactId>commons-parent</artifactId>
  <version>50</version>
  <packaging>pom</packaging>
  <name>Apache Commons Parent</name>
  <description>The Apache Commons Parent POM provides common settings for all Apache Commons components.</description>
  <url>https://commons.apache.org/commons-parent-pom.html</url>

  <!--
    In release 31, the maven.compile.* properties were corrected to maven.compiler.*
    [See COMMONSSITE-69]
    If updating from a previous version, please check the property definitions

    Starting with version 22, the RAT plugin has changed Maven group and id, so any existing configuration
    needs to be updated.
    To fix component POMs, please change any occurrences of:
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>rat-maven-plugin</artifactId>
    to the new values:
    <groupId>org.apache.rat</groupId>
    <artifactId>apache-rat-plugin</artifactId>

    Site deployment
    ===============
    Cannot define this here at present, see https://issues.apache.org/jira/browse/COMMONSSITE-26.

    The following should be added to the component POM:

    <distributionManagement>
    <site>
    <id>commons.site</id>
    <name>Apache Commons Site SVN</name>
    <url>scm:svn:${commons.scmPubUrl}</url>
    </site>
    </distributionManagement>

    Alternatively you can map the component's existing site id to the
    commons.scmPubServer property.

    Coverage tool selection
    =======================
    Starting with release 30, there are optional profiles for both Cobertura and JaCoCo.
    These can be enabled independently on the command-line:

    mvn site -Pcobertura -Pjacoco

    Or the component can define a default coverage tool by creating either (or both) of the following files:

    src/site/resources/profile.cobertura
    src/site/resources/profile.jacoco

    These can later be overridden by cancelling the profile:

    mvn site -P!jacoco

  -->


  <properties>
    <!-- configuration bits for cutting a release candidate, must be overridden by components -->
    <commons.release.version>${project.version}</commons.release.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.jira.id>COMMONSSITE</commons.jira.id>

    <!-- Default configuration for compiler source and target JVM -->
    <!-- Do NOT change this; it must remain as 1.3 -->
    <!--
      It's important that child POMs don't need to change when the parent POM is updated.
      At the time when these properties were introduced, the default Java version was 1.3.
      Thus components that failed to define the version would not be affected by updates
      to the Commons Parent or its parent the Apache pom.
      Of course most if not all components now define the properties.
      However it's still important to keep the properties as they effectively
      force child poms to define the Java version they require.
    -->
    <maven.compiler.source>1.3</maven.compiler.source>
    <maven.compiler.target>1.3</maven.compiler.target>

    <!-- compiler and surefire plugin settings for "java" profiles -->
    <commons.compiler.fork>false</commons.compiler.fork>
    <commons.compiler.compilerVersion />
    <commons.compiler.javac />

    <!-- plugin versions (allows same value in reporting and build sections; also allows easy override) -->
    <commons.animal-sniffer.version>1.18</commons.animal-sniffer.version>
    <!-- Almost all signatures use version 1.0. Allow override just in case -->
    <commons.animal-sniffer.signature.version>1.0</commons.animal-sniffer.signature.version>
    <commons.assembly-plugin.version>3.2.0</commons.assembly-plugin.version>
    <commons.build-helper.version>3.0.0</commons.build-helper.version>
    <commons.build-plugin.version>1.11</commons.build-plugin.version>
    <commons.changes.version>2.12.1</commons.changes.version>
    <commons.checkstyle-plugin.version>3.1.0</commons.checkstyle-plugin.version>
    <commons.clirr.version>2.8</commons.clirr.version>
    <commons.cobertura.version>2.7</commons.cobertura.version>
    <commons.compiler.version>3.8.1</commons.compiler.version>
    <commons.coveralls.version>4.3.0</commons.coveralls.version>
    <commons.coveralls.timestampFormat>EpochMillis</commons.coveralls.timestampFormat>
    <commons.failsafe.version>2.22.2</commons.failsafe.version>
    <commons.felix.version>4.2.1</commons.felix.version>
    <commons.findbugs.version>3.0.5</commons.findbugs.version>
    <commons.jacoco.version>0.8.5</commons.jacoco.version>
    <commons.japicmp.version>0.14.1</commons.japicmp.version>
    <commons.jar-plugin.version>3.2.0</commons.jar-plugin.version>
    <commons.javadoc.version>3.1.1</commons.javadoc.version>
    <commons.jdepend.version>2.0</commons.jdepend.version>
    <commons.jxr.version>3.0.0</commons.jxr.version>
    <commons.pmd.version>3.12.0</commons.pmd.version>
    <commons.project-info.version>3.0.0</commons.project-info.version>
    <commons.rat.version>0.13</commons.rat.version>
    <commons.release-plugin.version>1.7</commons.release-plugin.version>
    <commons.scm-publish.version>1.1</commons.scm-publish.version>

    <!--
      Note: Maven site plugin 3.5.1 is the latest version but is not a direct replacement:

      https://maven.apache.org/plugins/maven-site-plugin/migrate.html

      In particular, adding CDATA to header and footer sections is not backwards compatible.
      I.e. these have to be updated at the same time.

      Also it causes the following errors:

      [ERROR] Failed to execute goal org.apache.maven.plugins:maven-site-plugin:3.5.1:site (default-site) on project commons-parent:
      Execution default-site of goal org.apache.maven.plugins:maven-site-plugin:3.5.1:site failed:
      A required class was missing while executing org.apache.maven.plugins:maven-site-plugin:3.5.1:site: org/apache/maven/doxia/sink/impl/XhtmlBaseSink

      This is because Apache POM 17 forces an older version of Doxia core:
      https://mail-archives.apache.org/mod_mbox/maven-users/201602.mbox/%3C2337255.xU7aS9G1qr@herve-desktop%3E

      The same error applies when running with version 3.5.

      Since the version is defined as a property, the CP version can be overridden as follows if necessary:

      mvn site -Dcommons.site-plugin.version=3.5.1

      You will also need to add a dependency on Doxia core:
      <artifactId>maven-site-plugin</artifactId>
      <dependencies>
      <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-core</artifactId>
      <version>1.8</version>
      </dependency>
      </dependencies>

    -->
    <commons.site-plugin.version>3.8.2</commons.site-plugin.version>
    <commons.source-plugin.version>3.2.0</commons.source-plugin.version>
    <commons.spotbugs.version>3.1.6</commons.spotbugs.version>
    <commons.surefire-report.version>2.22.2</commons.surefire-report.version>
    <commons.surefire.version>2.22.2</commons.surefire.version>
    <commons.wagon-ssh.version>3.3.4</commons.wagon-ssh.version>

    <!-- Default values for the download-page generation by commons-build-plugin -->
    <commons.release.name>${project.artifactId}-${commons.release.version}</commons.release.name>
    <commons.release.desc />
    <commons.binary.suffix>-bin</commons.binary.suffix>
    <commons.release.2.name>${project.artifactId}-${commons.release.2.version}</commons.release.2.name>
    <commons.release.2.desc />
    <commons.release.2.binary.suffix>-bin</commons.release.2.binary.suffix>
    <commons.release.3.name>${project.artifactId}-${commons.release.3.version}</commons.release.3.name>
    <commons.release.3.desc />
    <commons.release.3.binary.suffix>-bin</commons.release.3.binary.suffix>
    <commons.release.4.desc />
    <commons.release.4.binary.suffix>-bin</commons.release.4.binary.suffix>

    <!-- Default values for the jacoco-maven-plugin reports -->
    <commons.jacoco.classRatio>1.00</commons.jacoco.classRatio>
    <commons.jacoco.instructionRatio>0.90</commons.jacoco.instructionRatio>
    <commons.jacoco.methodRatio>0.95</commons.jacoco.methodRatio>
    <commons.jacoco.branchRatio>0.85</commons.jacoco.branchRatio>
    <commons.jacoco.complexityRatio>0.85</commons.jacoco.complexityRatio>
    <commons.jacoco.lineRatio>0.90</commons.jacoco.lineRatio>
    <commons.jacoco.haltOnFailure>false</commons.jacoco.haltOnFailure>

    <!-- The Commons component id is used on the distribution server, for example:
         - Use dbcp instead of dbcp2.
         - Use collections instead of collections4.
         - Use lang instead of lang3.
         - Use pool instead of pool2.
         - and so on...
    -->
    <commons.componentid>${project.artifactId}</commons.componentid>

    <!-- The package id is substring of the package name from o.a.commons.(.*)., for example:
         - Use dbcp2 instead of dbcp.
         - Use collections4 instead of collections.
         - Use lang3 instead of lang.
         - Use pool2 instead of pool.
         - and so on...
    -->
    <commons.packageId>${project.artifactId}</commons.packageId>

    <!-- Configuration properties for the OSGi maven-bundle-plugin -->
    <commons.osgi.symbolicName>org.apache.commons.${commons.packageId}</commons.osgi.symbolicName>
    <commons.osgi.export>org.apache.commons.*;version=${project.version};-noimport:=true</commons.osgi.export>
    <commons.osgi.import>*</commons.osgi.import>
    <commons.osgi.dynamicImport />
    <commons.osgi.private />
    <commons.osgi.excludeDependencies>true</commons.osgi.excludeDependencies>

    <!-- location of any manifest file used by maven-jar-plugin -->
    <commons.manifestfile>${project.build.directory}/osgi/MANIFEST.MF</commons.manifestfile>

    <!--
      Make the deployment protocol pluggable. This allows to switch to
      other protocols like scpexe, which some users prefer over scp.
    -->
    <commons.deployment.protocol>scp</commons.deployment.protocol>

    <!--
      Encoding of Java source files: ensures that the compiler and
      the javadoc generator use the right encoding. Subprojects may
      overwrite this, if they are using another encoding.
    -->
    <commons.encoding>iso-8859-1</commons.encoding>
    <!-- used in this pom to provide the Javadoc HTML file encoding -->
    <commons.docEncoding>${commons.encoding}</commons.docEncoding>
    <!-- Define source encoding for filtering; used by general plugins -->
    <project.build.sourceEncoding>${commons.encoding}</project.build.sourceEncoding>
    <!-- This is used by reporting plugins -->
    <project.reporting.outputEncoding>${commons.encoding}</project.reporting.outputEncoding>

    <!-- Javadoc link to Java API. Default is Java 1.7; components can override to other versions -->
    <commons.javadoc6.java.link>http://docs.oracle.com/javase/6/docs/api/</commons.javadoc6.java.link>
    <commons.javadoc7.java.link>http://docs.oracle.com/javase/7/docs/api/</commons.javadoc7.java.link>
    <commons.javadoc8.java.link>http://docs.oracle.com/javase/8/docs/api/</commons.javadoc8.java.link>
    <commons.javadoc9.java.link>http://docs.oracle.com/javase/9/docs/api/</commons.javadoc9.java.link>
    <commons.javadoc10.java.link>http://docs.oracle.com/javase/10/docs/api/</commons.javadoc10.java.link>
    <commons.javadoc11.java.link>https://docs.oracle.com/en/java/javase/11/docs/api/</commons.javadoc11.java.link>
    <commons.javadoc12.java.link>https://docs.oracle.com/en/java/javase/12/docs/api/</commons.javadoc12.java.link>

    <commons.javadoc.java.link>${commons.javadoc7.java.link}</commons.javadoc.java.link>

    <commons.javadoc.javaee5.link>http://docs.oracle.com/javaee/5/api/</commons.javadoc.javaee5.link>
    <commons.javadoc.javaee6.link>http://docs.oracle.com/javaee/6/api/</commons.javadoc.javaee6.link>
    <commons.javadoc.javaee7.link>http://docs.oracle.com/javaee/7/api/</commons.javadoc.javaee7.link>

    <commons.javadoc.javaee.link>${commons.javadoc.javaee6.link}</commons.javadoc.javaee.link>

    <!-- build meta inf -->
    <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>
    <implementation.build>${scmBranch}@r${buildNumber}; ${maven.build.timestamp}</implementation.build>

    <!-- Allow Clirr severity to be overriden by the command-line option -DminSeverity=level -->
    <minSeverity>info</minSeverity>

    <!-- Control number of issues retrieved from JIRA with changes plugin -->
    <commons.changes.maxEntries>100</commons.changes.maxEntries>

    <!-- Allow surefire-report aggregation to be easily configured for multi-module projects -->
    <commons.surefire-report.aggregate>false</commons.surefire-report.aggregate>

    <!-- Allow changes Jira report to be restricted to just the current version (plugin default is false) -->
    <commons.changes.onlyCurrentVersion>false</commons.changes.onlyCurrentVersion>
    <!-- Allow changes Jira report maxEntries to be overridden (plugin default 100) -->
    <commons.changes.maxEntries>100</commons.changes.maxEntries>
    <!-- Allow changes Jira report runOnlyAtExecutionRoot to be overridden (plugin default is false) -->
    <commons.changes.runOnlyAtExecutionRoot>false</commons.changes.runOnlyAtExecutionRoot>

    <!-- scm publish plugin configuration -->
    <commons.site.cache>${user.home}/commons-sites</commons.site.cache>
    <!-- value modules can override it -->
    <commons.site.path>${commons.componentid}</commons.site.path>

    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-${commons.componentid}</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>${commons.site.cache}/${commons.site.path}</commons.scmPubCheckoutDirectory>
    <commons.scmPubServer>commons.site</commons.scmPubServer>

    <!-- allow japicmp's breakBuildOnBinaryIncompatibleModifications
      to be overridden, plugin's default is false -->
    <commons.japicmp.breakBuildOnBinaryIncompatibleModifications>true</commons.japicmp.breakBuildOnBinaryIncompatibleModifications>
    <commons.japicmp.breakBuildOnSourceIncompatibleModifications>false</commons.japicmp.breakBuildOnSourceIncompatibleModifications>
    <commons.japicmp.ignoreMissingClasses>false</commons.japicmp.ignoreMissingClasses>

    <!-- Commons Release plugin: dist dev site -->
    <commons.distSvnStagingUrl>scm:svn:https://dist.apache.org/repos/dist/dev/commons/${commons.componentid}</commons.distSvnStagingUrl>

    <!-- Commons Release plugin: release manager -->
    <commons.releaseManagerName>${user.name}</commons.releaseManagerName>
    <commons.releaseManagerKey>DEADBEEF</commons.releaseManagerKey>

    <sonar.host.url>https://analysis.apache.org/</sonar.host.url>

  </properties>

  <mailingLists>
    <!-- N.B. commons-site now uses the Apache POM so has its own copy of the mailing list definitions -->
    <!--
      Components should normally override the default mailing list report by using the comnand
      mvn commons-build:mail-page
      This generates the file src/site/xdoc/mail-lists.xml which when processed will replace the PIR version.
    -->
    <!-- Changes to this list should be synchronised with the commons build plugin -->
    <mailingList>
      <name>Commons User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/commons-user/</archive>
      <otherArchives>
        <otherArchive>https://markmail.org/list/org.apache.commons.users/</otherArchive>
        <otherArchive>https://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/commons-dev/</archive>
      <otherArchives>
        <otherArchive>https://markmail.org/list/org.apache.commons.dev/</otherArchive>
        <otherArchive>https://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>https://mail-archives.apache.org/mod_mbox/commons-issues/</archive>
      <otherArchives>
        <otherArchive>https://markmail.org/list/org.apache.commons.issues/</otherArchive>
        <otherArchive>https://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>https://mail-archives.apache.org/mod_mbox/commons-commits/</archive>
      <otherArchives>
        <otherArchive>https://markmail.org/list/org.apache.commons.commits/</otherArchive>
        <otherArchive>https://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>https://mail-archives.apache.org/mod_mbox/www-announce/</archive>
      <otherArchives>
        <otherArchive>https://markmail.org/list/org.apache.announce/</otherArchive>
        <otherArchive>https://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <prerequisites>
    <maven>3.0.5</maven>
  </prerequisites>

  <!--
    This section *must* be overwritten by subprojects. It is only to allow
    a release of the commons-parent POM.
  -->
  <scm>
    <connection>scm:git:http://gitbox.apache.org/repos/asf/commons-parent.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-parent.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=commons-parent.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/COMMONSSITE</url>
  </issueManagement>

  <ciManagement>
    <system>jenkins</system>
    <url>https://builds.apache.org/</url>
  </ciManagement>

  <build>
    <!-- TODO find a better way to add N&L files to jars and test jars
      See also maven-remote-resources-plugin configuration below.
    -->
    <resources>
      <!-- This is the default setting from the super-pom -->
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <!-- hack to ensure the N&L appear in jars -->
      <resource>
        <directory>${basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </resource>
    </resources>
    <!-- ensure test jars also get NOTICE & LICENSE files -->
    <testResources>
      <!-- This is the default setting from the super-pom -->
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
      <!-- hack to ensure the N&L appear in jars -->
      <testResource>
        <directory>${basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <!-- org.apache.maven.plugins, alpha order by artifact id -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${commons.compiler.version}</version>
          <configuration>
            <source>${maven.compiler.source}</source>
            <target>${maven.compiler.target}</target>
            <encoding>${commons.encoding}</encoding>
            <!--
              fork is set true by the java-1.x profiles
              This allows the use of a different version of the compiler from the
              JDK being used to run Maven
            -->
            <fork>${commons.compiler.fork}</fork>
            <!-- the following are only needed if fork is true -->
            <compilerVersion>${commons.compiler.compilerVersion}</compilerVersion>
            <executable>${commons.compiler.javac}</executable>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${commons.assembly-plugin.version}</version>
        </plugin>
        <!-- Apache parent includes docck -->
        <!-- Apache parent: invoker -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${commons.javadoc.version}</version>
          <configuration>
            <!-- keep only errors and warnings -->
            <quiet>true</quiet>
            <source>${maven.compiler.source}</source>
            <encoding>${commons.encoding}</encoding>
            <docEncoding>${commons.docEncoding}</docEncoding>
            <notimestamp>true</notimestamp>
            <links>
              <link>${commons.javadoc.java.link}</link>
              <link>${commons.javadoc.javaee.link}</link>
            </links>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <!-- TODO see above - find better way to add N&L files to jars and test jars -->
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <configuration>
            <!--
              Apache parent POM automatically adds "LICENSE" and "NOTICE" files
              to jars - duplicating the "LICENSE.txt" and "NOTICE.txt"
              files that components already have.
            -->
            <skip>true</skip>
          </configuration>
        </plugin>
        <!-- Apache parent: scm -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${commons.site-plugin.version}</version>
          <configuration>
            <!-- don't deploy site with maven-site-plugin -->
            <skipDeploy>true</skipDeploy>
          </configuration>
          <dependencies>
            <dependency>
              <!-- add support for ssh/scp -->
              <groupId>org.apache.maven.wagon</groupId>
              <artifactId>wagon-ssh</artifactId>
              <version>${commons.wagon-ssh.version}</version>
            </dependency>
          </dependencies>
          <executions>
            <execution>
              <id>attach-descriptor</id>
              <goals>
                <goal>attach-descriptor</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${commons.source-plugin.version}</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${commons.surefire.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${commons.failsafe.version}</version>
        </plugin>
        <!-- Other plugins, alpha order by groupId and artifactId -->
        <plugin>
          <groupId>com.github.siom79.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <version>${commons.japicmp.version}</version>
          <configuration>
            <oldVersion>
              <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>${project.artifactId}</artifactId>
                <version>${commons.bc.version}</version>
                <type>jar</type>
             </dependency>
            </oldVersion>
            <newVersion>
              <file>
                <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
              </file>
            </newVersion>
            <parameter>
              <onlyModified>true</onlyModified>
              <breakBuildOnBinaryIncompatibleModifications>${commons.japicmp.breakBuildOnBinaryIncompatibleModifications}</breakBuildOnBinaryIncompatibleModifications>
              <breakBuildOnSourceIncompatibleModifications>${commons.japicmp.breakBuildOnSourceIncompatibleModifications}</breakBuildOnSourceIncompatibleModifications>
              <!-- skip japicmp on "mvn site" - use "mvn package site" to include report -->
              <ignoreMissingNewVersion>true</ignoreMissingNewVersion>
              <reportOnlyFilename>true</reportOnlyFilename>
              <skipPomModules>true</skipPomModules>
              <ignoreMissingClasses>${commons.japicmp.ignoreMissingClasses}</ignoreMissingClasses>
              <overrideCompatibilityChangeParameters>
                <overrideCompatibilityChangeParameter>
                  <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                  <binaryCompatible>true</binaryCompatible>
                  <sourceCompatible>true</sourceCompatible>
                  <semanticVersionLevel>PATCH</semanticVersionLevel>
                </overrideCompatibilityChangeParameter>
              </overrideCompatibilityChangeParameters>
            </parameter>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-build-plugin</artifactId>
          <version>${commons.build-plugin.version}</version>
          <configuration>
            <commons.release.name>${commons.release.name}</commons.release.name>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${commons.felix.version}</version>
          <inherited>true</inherited>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${commons.rat.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${commons.build-helper.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${commons.clirr.version}</version>
          <configuration>
            <minSeverity>${minSeverity}</minSeverity>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <!-- Version 2.2 causes an NPE with Maven 3.3.9 -->
          <version>2.7</version>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${commons.jacoco.version}</version>
          <!-- Note that since JaCoCo relies on an agent to perform tests,
            it changes the surefire arguments line. If a component also
            needs to change the argument line of maven-surefire-plugin,
            then it must add ${argLine} property (which is set by JaCoCo)
            in the argLine configuration element of maven-surefire-plugin
            to preserve JaCoCo settings. -->
          <executions>
            <execution>
              <id>prepare-agent</id>
              <phase>process-test-classes</phase>
              <goals>
                <goal>prepare-agent</goal>
              </goals>
            </execution>
            <execution>
              <id>report</id>
              <phase>site</phase>
              <goals>
                <goal>report</goal>
              </goals>
            </execution>
            <execution>
              <id>check</id>
              <goals>
                <goal>check</goal>
              </goals>
              <configuration>
                <rules>
                  <rule>
                    <element>BUNDLE</element>
                    <limits>
                      <limit>
                        <counter>CLASS</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.classRatio}</minimum>
                      </limit>
                      <limit>
                        <counter>INSTRUCTION</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.instructionRatio}</minimum>
                      </limit>
                      <limit>
                        <counter>METHOD</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.methodRatio}</minimum>
                      </limit>
                      <limit>
                        <counter>BRANCH</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.branchRatio}</minimum>
                      </limit>
                      <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.lineRatio}</minimum>
                      </limit>
                      <limit>
                        <counter>COMPLEXITY</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>${commons.jacoco.complexityRatio}</minimum>
                      </limit>
                    </limits>
                  </rule>
                </rules>
                <haltOnFailure>${commons.jacoco.haltOnFailure}</haltOnFailure>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${commons.project-info.version}</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.bcel</groupId>
              <artifactId>bcel</artifactId>
              <version>6.4.1</version>
            </dependency>
          </dependencies>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <!-- org.apache.maven.plugins, alpha order by artifact id -->
      <plugin>
        <!-- Parent POM is released, so needs source archive for ASF mirrors -->
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <!--
          - Copy LICENSE.txt and NOTICE.txt so that they are included
          - in the -javadoc jar file for the component.
        -->
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>javadoc.resources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy todir="${project.build.directory}/apidocs/META-INF">
                  <fileset dir="${basedir}">
                    <include name="LICENSE.txt" />
                    <include name="NOTICE.txt" />
                  </fileset>
                </copy>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <!-- Unfortunately the much simpler
        <prerequisites><maven>3.0</maven></prerequisites>
        is not inherited so we have to use the enforcer plugin
      -->
      <plugin>
        <inherited>true</inherited>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M2</version>
        <configuration>
          <rules>
            <requireMavenVersion>
              <version>3.0.5</version>
            </requireMavenVersion>
            <requireJavaVersion>
              <version>${maven.compiler.target}</version>
            </requireJavaVersion>
          </rules>
          <fail>true</fail>
        </configuration>
        <executions>
          <execution>
            <id>enforce-maven-3</id>
            <goals>
              <goal>enforce</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${commons.jar-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <!-- Avoids an error when releasing the parent pom -->
              <skipIfEmpty>true</skipIfEmpty>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <archive>
            <manifestFile>${commons.manifestfile}</manifestFile>
            <manifestEntries>
              <Specification-Title>${project.name}</Specification-Title>
              <Specification-Version>${project.version}</Specification-Version>
              <Specification-Vendor>${project.organization.name}</Specification-Vendor>
              <Implementation-Title>${project.name}</Implementation-Title>
              <Implementation-Version>${project.version}</Implementation-Version>
              <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
              <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
              <Implementation-Build>${implementation.build}</Implementation-Build>
              <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
              <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>create-source-jar</id>
            <goals>
              <goal>jar-no-fork</goal>
              <goal>test-jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--
            commons.surefire.java is normally empty.
            It is defined by the java-1.x profiles to change the JVM used by Surefire
          -->
          <jvm>${commons.surefire.java}</jvm>
        </configuration>
      </plugin>
      <!-- Other plugins, alpha order by groupId and artifactId -->
      <plugin>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-build-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <!--
            dummy entry to stop bundle plugin from picking up jar config and reporting
            WARNING: Duplicate name in Manifest
            See http://markmail.org/message/mpkl24wk3jrjhhjg
          -->
          <archive>
            <forced>true</forced>
          </archive>
          <excludeDependencies>${commons.osgi.excludeDependencies}</excludeDependencies>
          <manifestLocation>${project.build.directory}/osgi</manifestLocation>
          <instructions>
            <!-- stops the "uses" clauses being added to "Export-Package" manifest entry -->
            <_nouses>true</_nouses>
            <!-- Stop the JAVA_1_n_HOME variables from being treated as headers by Bnd -->
            <_removeheaders>JAVA_1_3_HOME,JAVA_1_4_HOME,JAVA_1_5_HOME,JAVA_1_6_HOME,JAVA_1_7_HOME,JAVA_1_8_HOME,JAVA_1_9_HOME</_removeheaders>
            <Bundle-SymbolicName>${commons.osgi.symbolicName}</Bundle-SymbolicName>
            <Export-Package>${commons.osgi.export}</Export-Package>
            <Private-Package>${commons.osgi.private}</Private-Package>
            <Import-Package>${commons.osgi.import}</Import-Package>
            <DynamicImport-Package>${commons.osgi.dynamicImport}</DynamicImport-Package>
            <Bundle-DocURL>${project.url}</Bundle-DocURL>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Needed for command-line access, e.g mvn apache-rat:rat and mvn apache-rat:check -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <!-- Should agree with config in reporting section -->
        <configuration>
          <!--
            If you wish to override this list in the component (child) pom, ensure you use
            <excludes combine.children="merge">
            so that the child pom entries replace the parent entries
          -->
          <excludes combine.children="append">
            <exclude>site-content/**</exclude>
            <exclude>.checkstyle</exclude>
            <exclude>.fbprefs</exclude>
            <exclude>.pmd</exclude>
            <exclude>src/site/resources/download_*.cgi</exclude>
            <exclude>src/site/resources/profile.*</exclude>
            <exclude>profile.*</exclude>
            <!-- Exclude Eclipse local files and folders -->
            <exclude>maven-eclipse.xml</exclude>
            <exclude>.externalToolBuilders/**</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <id>rat-check</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <content>${project.reporting.outputDirectory}</content>
          <pubScmUrl>scm:svn:${commons.scmPubUrl}</pubScmUrl>
          <checkoutDirectory>${commons.scmPubCheckoutDirectory}</checkoutDirectory>
          <serverId>${commons.scmPubServer}</serverId>
          <tryUpdate>true</tryUpdate>
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

  <reporting>
    <!-- N.B. plugins defined here in the <reporting> section ignore what's defined in <pluginManagement>
      in the <build> section above, so we have to define the versions here. -->
    <plugins>
      <!-- org.apache.maven.plugins, alpha order by artifact id -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${commons.changes.version}</version>
        <configuration>
          <xmlPath>${basedir}/src/changes/changes.xml</xmlPath>
          <columnNames>Fix Version,Key,Component,Summary,Type,Resolution,Status</columnNames>
          <!-- Sort cols in natural order when using JQL for JIRA 5.1 -->
          <sortColumnNames>Fix Version DESC,Type,Key DESC</sortColumnNames>
          <resolutionIds>Fixed</resolutionIds>
          <statusIds>Resolved,Closed</statusIds>
          <!-- Don't include sub-task -->
          <typeIds>Bug,New Feature,Task,Improvement,Wish,Test</typeIds>
          <!-- For JIRA >= 5.1 -->
          <useJql>true</useJql>
          <onlyCurrentVersion>${commons.changes.onlyCurrentVersion}</onlyCurrentVersion>
          <maxEntries>${commons.changes.maxEntries}</maxEntries>
          <runOnlyAtExecutionRoot>${commons.changes.runOnlyAtExecutionRoot}</runOnlyAtExecutionRoot>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${commons.javadoc.version}</version>
        <configuration>
          <!-- keep only errors and warnings -->
          <quiet>true</quiet>
          <source>${maven.compiler.source}</source>
          <encoding>${commons.encoding}</encoding>
          <docencoding>${commons.docEncoding}</docencoding>
          <notimestamp>true</notimestamp>
          <linksource>true</linksource>
          <!-- prevent svnpub to be too much noisy -->
          <notimestamp>true</notimestamp>
          <links>
            <link>${commons.javadoc.java.link}</link>
            <link>${commons.javadoc.javaee.link}</link>
          </links>
        </configuration>
        <!-- Suppress test Javadocs -->
        <reportSets>
          <reportSet>
            <id>default</id>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${commons.jxr.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>${commons.project-info.version}</version>
        <!--
          Unfortunately it does not appear to be possible to override
          reports configured in a parent POM.
          See https://jira.codehaus.org/browse/MPIR-241
          and https://issues.apache.org/jira/browse/MPOM-32
          So we define here all those reports that are suitable for
          all components.
          Components can add extra reports if they wish, but cannot disable any.
        -->
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>summary</report>
              <report>modules</report>
              <!-- <report>license</report> site must link to ASF page instead -->
              <report>team</report>
              <report>scm</report>
              <report>issue-management</report>
              <report>mailing-lists</report>
              <report>dependency-info</report>
              <report>dependency-management</report>
              <report>dependencies</report>
              <report>dependency-convergence</report>
              <report>ci-management</report>
              <!-- <report>plugin-management</report> not very useful for end users -->
              <!-- <report>plugins</report> not very useful for end users -->
              <report>distribution-management</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>${commons.site-plugin.version}</version>
        <configuration>
          <!-- Exclude the navigation file for Maven 1 sites
            and the changes file used by the changes-plugin,
            as they interfere with the site generation. -->
          <moduleExcludes>
            <xdoc>navigation.xml,changes.xml</xdoc>
          </moduleExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${commons.surefire-report.version}</version>
        <configuration>
          <aggregate>${commons.surefire-report.aggregate}</aggregate>
        </configuration>
      </plugin>
      <!-- Other plugins, alpha order by groupId and artifactId -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${commons.rat.version}</version>
        <!-- Should agree with config in build section -->
        <configuration>
          <!--
            If you wish to override this list in the component (child) pom, ensure you use
            <excludes combine.children="merge">
            so that the child pom entries replace the parent entries
          -->
          <excludes combine.children="append">
            <exclude>site-content/**</exclude>
            <exclude>.checkstyle</exclude>
            <exclude>.fbprefs</exclude>
            <exclude>.pmd</exclude>
            <exclude>src/site/resources/download_*.cgi</exclude>
            <exclude>src/site/resources/profile.*</exclude>
            <exclude>profile.*</exclude>
            <!-- Exclude Eclipse local files and folders -->
            <exclude>maven-eclipse.xml</exclude>
            <exclude>.externalToolBuilders/**</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
        <version>${commons.jdepend.version}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>svn</id>
      <activation>
        <file>
          <exists>.svn</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>buildnumber-maven-plugin</artifactId>
            <executions>
              <execution>
                <phase>validate</phase>
                <goals>
                  <goal>create</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <!-- Use committed revision so it does not change every time svn update is run -->
              <useLastCommittedRevision>true</useLastCommittedRevision>
              <!-- default revision number if unavailable -->
              <revisionOnScmFailure>??????</revisionOnScmFailure>
              <!-- svnjava works even if an svn command-line client is not on the path -->
              <providerImplementations>
                <svn>javasvn</svn>
              </providerImplementations>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!--
        Add an automatic module name to the manifest if the file
        'profile.module-name' exists. The name must be provided in the property
        'commons.module.name'.
      -->
      <id>module-name</id>
      <activation>
        <file>
          <exists>profile.module-name</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
              <archive combine.children="append">
                <manifestEntries>
                  <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
                </manifestEntries>
              </archive>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!--
        Make the version parts of the maven.compiler.target property
        available as separate properties, so that they can be used to define
        the java signature artifactId used by animal-sniffer etc.
      -->
      <id>parse-target-version</id>
      <activation>
        <property>
          <!--
            This property should always be defined.
            The intention is to ensure that the profile is always enabled.
            (activeByDefault only applies if other profiles are not enabled)
          -->
          <name>user.home</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>parse-version</id>
                <!-- default is: <phase>validate</phase> -->
                <goals>
                  <goal>parse-version</goal>
                </goals>
                <configuration>
                  <propertyPrefix>javaTarget</propertyPrefix>
                  <versionString>${maven.compiler.target}</versionString>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
      Runs the Animal Sniffer plugin to check that generated code does not included references
      to methods/classes etc that are not present in the standard Java runtime for the defined target version.
      To bypass the checks, define "animal.sniffer.skip" as true, or create the file "src/site/resources/profile.noanimal"
    -->

    <profile>
      <id>animal-sniffer</id>
      <activation>
        <!-- active unless the file is found -->
        <file>
          <missing>src/site/resources/profile.noanimal</missing>
        </file>
      </activation>

      <properties>
        <!-- define this as a property to allow command-line override -->
        <animal-sniffer.signature>java${javaTarget.majorVersion}${javaTarget.minorVersion}</animal-sniffer.signature>
      </properties>

      <build>
        <plugins>

          <!-- Run the Animal Sniffer checks -->
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>animal-sniffer-maven-plugin</artifactId>
            <version>${commons.animal-sniffer.version}</version>
            <executions>
              <execution>
                <id>checkAPIcompatibility</id>
                <!-- default is: <phase>process-classes</phase> -->
                <!-- Note: if the plugin is ever enhanced to check test classes
                  it will presumably need to be bound to process-test-classes instead
                -->
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <signature>
                <groupId>org.codehaus.mojo.signature</groupId>
                <artifactId>${animal-sniffer.signature}</artifactId>
                <version>${commons.animal-sniffer.signature.version}</version>
              </signature>
            </configuration>
          </plugin>

        </plugins>
      </build>
    </profile>

    <profile>
      <id>jacoco</id>
      <activation>
        <!--
          N.B. in spite of what the Maven docs may say,
          activation conditions are ORed together, see:
          http://jira.codehaus.org/browse/MNG-4565
          Fairly useless, but that's what was done in
          http://jira.codehaus.org/browse/MNG-3106

          So we cannot also check for Java 1.5+
          This should not be a problem now as the profile is optional.
          Components that still target Java 1.4 or earlier
          just need to ensure they don't use JaCoCo by default.
        -->
        <file>
          <exists>src/site/resources/profile.jacoco</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${commons.jacoco.version}</version>
          </plugin>
        </plugins>
      </build>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${commons.jacoco.version}</version>
            <reportSets>
              <reportSet>
                <reports>
                  <!-- select non-aggregate reports -->
                  <report>report</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <profile>
      <id>cobertura</id>
      <activation>
        <file>
          <exists>src/site/resources/profile.cobertura</exists>
        </file>
      </activation>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
            <version>${commons.cobertura.version}</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <profile>
      <id>clirr</id>
      <activation>
        <file>
          <exists>src/site/resources/profile.clirr</exists>
        </file>
      </activation>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <version>${commons.clirr.version}</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <!-- alternative to clirr, will fail build if binary compatibility is broken -->
    <profile>
      <id>japicmp</id>
      <activation>
        <jdk>[1.8,)</jdk>
        <file>
          <exists>src/site/resources/profile.japicmp</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>com.github.siom79.japicmp</groupId>
            <artifactId>japicmp-maven-plugin</artifactId>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>cmp</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <reporting>
        <plugins>
          <plugin>
            <groupId>com.github.siom79.japicmp</groupId>
            <artifactId>japicmp-maven-plugin</artifactId>
            <version>${commons.japicmp.version}</version>
            <configuration>
              <parameter>
                <onlyModified>true</onlyModified>
                <breakBuildOnBinaryIncompatibleModifications>${commons.japicmp.breakBuildOnBinaryIncompatibleModifications}</breakBuildOnBinaryIncompatibleModifications>
                <breakBuildOnSourceIncompatibleModifications>${commons.japicmp.breakBuildOnSourceIncompatibleModifications}</breakBuildOnSourceIncompatibleModifications>
                <!-- skip japicmp on "mvn site" - use "mvn package site" to include report -->
                <ignoreMissingNewVersion>true</ignoreMissingNewVersion>
                <reportOnlyFilename>true</reportOnlyFilename>
                <skipPomModules>true</skipPomModules>
                <ignoreMissingClasses>${commons.japicmp.ignoreMissingClasses}</ignoreMissingClasses>
                <overrideCompatibilityChangeParameters>
                  <overrideCompatibilityChangeParameter>
                    <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                    <binaryCompatible>true</binaryCompatible>
                    <sourceCompatible>true</sourceCompatible>
                    <semanticVersionLevel>PATCH</semanticVersionLevel>
                  </overrideCompatibilityChangeParameter>
                </overrideCompatibilityChangeParameters>
              </parameter>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <!--
      Profile for Commons releases via Nexus.
      Assembles artifacts, creates source and javadoc jars, signs them and adds hashes.
    -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- Pass these arguments to the deploy plugin. -->
              <arguments>-Prelease</arguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>create-javadoc-jar</id>
                <goals>
                  <goal>javadoc</goal>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
            <configuration>
              <source>${maven.compiler.source}</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <version>${commons.assembly-plugin.version}</version>
            <inherited>true</inherited>
            <executions>
              <execution>
                <goals>
                  <goal>single</goal>
                </goals>
                <!-- COMMONSSITE-87 Ensure this runs after all package phase plugins -->
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-release-plugin</artifactId>
            <version>${commons.release-plugin.version}</version>
            <executions>
              <execution>
                <id>clean-staging</id>
                <phase>clean</phase>
                <goals>
                  <goal>clean-staging</goal>
                </goals>
              </execution>
              <execution>
                <id>detatch-distributions</id>
                <phase>verify</phase>
                <goals>
                  <goal>detach-distributions</goal>
                </goals>
              </execution>
              <execution>
                <id>stage-distributions</id>
                <phase>deploy</phase>
                <goals>
                  <goal>stage-distributions</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
      profile to update the Apache parent pom profile of the same name
      to better suit the requirements of Apache Commons.
      [Requires further work]
    -->
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <releaseProfiles>apache-release</releaseProfiles>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-test-sources</id>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
      Profile for running the build using JDK 1.3
      (JAVA_1_3_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.3</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.3</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_3_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_3_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.4
      (JAVA_1_4_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.4</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.4</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_4_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_4_HOME}/bin/java</commons.surefire.java>
        <!-- later versions of surefire don't support Java 1.4 -->
        <commons.surefire.version>2.11</commons.surefire.version>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.5
      (JAVA_1_5_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.5</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.5</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_5_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_5_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.6
      (JAVA_1_6_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.6</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.6</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_6_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_6_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.7
      (JAVA_1_7_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.7</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.7</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_7_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_7_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.8
      (JAVA_1_8_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.8</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.8</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_8_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_8_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.9
      (JAVA_1_9_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.9</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.9</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_9_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_9_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.10
      (JAVA_1_10_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.10</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.10</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_10_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_10_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.11
      (JAVA_1_11_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.11</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.11</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_11_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_11_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
      Profile for running the build using JDK 1.12
      (JAVA_1_12_HOME needs to be defined, e.g. in settings.xml or an environment variable)
    -->
    <profile>
      <id>java-1.12</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.12</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_12_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_12_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!-- N.B. when adding new java profiles, be sure to update
      the _removeheaders list in the maven_bundle_plugin configuration -->

    <!--
      | Profile to allow testing of deploy phase
      | e.g.
      | mvn deploy -Ptest-deploy -Prelease -Dgpg.skip
    -->
    <profile>
      <id>test-deploy</id>
      <properties>
        <altDeploymentRepository>id::default::file:target/deploy</altDeploymentRepository>
        <commons.release.dryRun>true</commons.release.dryRun>
      </properties>
    </profile>

    <profile>
      <!--
        Generate release notes in top-level directory from src/changes/changes.xml
        Usage:
        mvn changes:announcement-generate -Prelease-notes [-Dchanges.version=nnn]

        Defining changes.version allows one to create the RN without first removing the SNAPSHOT suffix.

        Requires file src/changes/release-notes.vm.
        A sample template is available from:
        https://svn.apache.org/repos/asf/commons/proper/commons-parent/trunk/src/changes/release-notes.vm
      -->
      <id>release-notes</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-changes-plugin</artifactId>
            <version>${commons.changes.version}</version>
            <configuration>
              <template>release-notes.vm</template>
              <templateDirectory>src/changes</templateDirectory>
              <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
              <announcementDirectory>.</announcementDirectory>
              <announcementFile>RELEASE-NOTES.txt</announcementFile>
              <announceParameters>
                <releaseVersion>${commons.release.version}</releaseVersion>
              </announceParameters>
            </configuration>
            <executions>
              <execution>
                <id>create-release-notes</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>announcement-generate</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
      Automatically run the buildnumber plugin unless the buildNumber.skip property is defined as true
    -->
    <profile>
      <id>svn-buildnumber</id>
      <activation>
        <property>
          <name>!buildNumber.skip</name>
          <value>!true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>buildnumber-maven-plugin</artifactId>
            <executions>
              <execution>
                <phase>generate-resources</phase>
                <goals>
                  <goal>create</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <!-- Use committed revision so it does not change every time svn update is run -->
              <useLastCommittedRevision>true</useLastCommittedRevision>
              <!-- default revision number if unavailable -->
              <revisionOnScmFailure>??????</revisionOnScmFailure>
              <doCheck>false</doCheck>
              <doUpdate>false</doUpdate>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- optional profile to use javasvn instead of the SVN CLI for the buildNumber plugin -->
    <profile>
      <id>javasvn</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>buildnumber-maven-plugin</artifactId>
            <configuration>
              <providerImplementations>
                <svn>javasvn</svn>
              </providerImplementations>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- profile to allow the use of plugin versions that require Java 7 -->
    <profile>
      <id>jdk7-plugin-fix-version</id>
      <activation>
        <jdk>[1.7,1.8)</jdk>
      </activation>
      <properties>
        <!-- Fix to build on JDK 7: version 4.0.0 requires Java 8. -->
        <commons.felix.version>3.5.1</commons.felix.version>
      </properties>
    </profile>

    <!-- allow simple creation of the site without any optional reports -->
    <profile>
      <id>site-basic</id>
      <properties>
        <skipTests>true</skipTests>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <cobertura.skip>true</cobertura.skip>
        <findbugs.skip>true</findbugs.skip>
        <spotbugs.skip>true</spotbugs.skip>
        <checkstyle.skip>true</checkstyle.skip>
        <clirr.skip>true</clirr.skip>
        <changes.jira.skip>true</changes.jira.skip>
        <rat.skip>true</rat.skip> <!-- from version 0.12 -->
        <jacoco.skip>true</jacoco.skip>
        <skipSurefireReport>true</skipSurefireReport>
      </properties>
    </profile>

    <profile>
      <id>travis-cobertura</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
            <version>${commons.cobertura.version}</version>
            <configuration>
              <formats>
                <format>xml</format>
              </formats>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.eluder.coveralls</groupId>
            <artifactId>coveralls-maven-plugin</artifactId>
            <version>${commons.coveralls.version}</version>
            <configuration>
              <timestampFormat>${commons.coveralls.timestampFormat}</timestampFormat>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>travis-jacoco</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>${commons.jacoco.version}</version>
          </plugin>
          <plugin>
            <groupId>org.eluder.coveralls</groupId>
            <artifactId>coveralls-maven-plugin</artifactId>
            <version>${commons.coveralls.version}</version>
            <configuration>
              <timestampFormat>${commons.coveralls.timestampFormat}</timestampFormat>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>

</project>
