<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>65</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <name>Apache Commons Exec</name>
  <artifactId>commons-exec</artifactId>
  <version>1.4.0</version>

  <inceptionYear>2005</inceptionYear>
  <description>Apache Commons Exec is a library to reliably execute external processes from within the JVM.</description>

  <url>https://commons.apache.org/proper/commons-exec/</url>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Website</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-exec/</url>
    </site>
  </distributionManagement>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/EXEC</url>
  </issueManagement>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/commons-exec</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-exec</developerConnection>
    <url>https://gitbox.apache.org/repos/asf/commons-exec</url>
  </scm>

  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.bc.version>1.3</commons.bc.version>
    <commons.release.version>1.4.0</commons.release.version>
    <commons.release.next>1.4.1</commons.release.next>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.componentid>exec</commons.componentid>
    <commons.module.name>org.apache.commons.exec</commons.module.name>
    <commons.jira.id>EXEC</commons.jira.id>
    <commons.jira.pid>12310814</commons.jira.pid>
    <!-- The RC version used in the staging repository URL. -->
    <!-- Don't use platform specific encodings for copying resources -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>
    <test>*Test</test>
    <project.build.outputTimestamp>2024-01-01T11:44:20Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit-pioneer</groupId>
      <artifactId>junit-pioneer</artifactId>
      <!-- Version 2.x requires Java 11 -->
      <version>1.9.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <defaultGoal>clean artifact:check-buildplan verify apache-rat:check japicmp:cmp checkstyle:check pmd:cpd-check pmd:check javadoc:javadoc spotbugs:check</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <configuration>
            <configLocation>${basedir}/src/conf/checkstyle.xml</configLocation>
            <enableRulesSummary>false</enableRulesSummary>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <configuration>
            <threshold>Normal</threshold>
            <effort>Default</effort>
            <excludeFilterFile>${basedir}/src/conf/findbugs-exclude-filter.xml</excludeFilterFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${commons.javadoc.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <mavenExecutorId>forked-path</mavenExecutorId>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <ignorePathsToDelete>
            <ignorePathToDelete>javadocs</ignorePathToDelete>
          </ignorePathsToDelete>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <!-- generate the changes report from changes.xml -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <configuration>
          <issueLinkTemplate>%URL%/%ISSUE%</issueLinkTemplate>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/test/resources/eml/*.eml</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <developers>
    <developer>
      <id>brett</id>
      <name>Brett Porter</name>
      <organization>Apache</organization>
      <timezone>+10</timezone>
    </developer>
    <developer>
      <id>trygvis</id>
      <name>Trygve Laugstøl</name>
      <organization>Apache</organization>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>sgoeschl</id>
      <name>Siegfried Goeschl</name>
      <organization>Apache</organization>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>sebb</id>
      <name>Sebastian Bazley</name>
      <organization>Apache</organization>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email>ggregory at apache.org</email>
      <url>https://www.garygregory.com</url>
      <organization>The Apache Software Foundation</organization>
      <organizationUrl>https://www.apache.org/</organizationUrl>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New_York</timezone>
      <properties>
        <picUrl>https://people.apache.org/~ggregory/img/garydgregory80.png</picUrl>
      </properties>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Niklas Gustavsson</name>
    </contributor>
    <contributor>
      <name>Benjamin Bentmann</name>
    </contributor>
    <contributor>
      <name>Marco Ferrante</name>
    </contributor>
    <contributor>
      <name>Jerome Lacoste</name>
    </contributor>
    <contributor>
      <name>Milos Kleint</name>
    </contributor>
    <contributor>
      <name>Pablo Hoertner</name>
    </contributor>
    <contributor>
      <name>Niall Pemberton</name>
    </contributor>
  </contributors>

</project>
