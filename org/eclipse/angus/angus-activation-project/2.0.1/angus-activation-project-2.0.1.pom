<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2023 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.7</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.eclipse.angus</groupId>
    <artifactId>angus-activation-project</artifactId>
    <packaging>pom</packaging>
    <version>2.0.1</version>
    <name>Angus Activation Project</name>
    <description>${project.name}</description>
    <url>https://github.com/eclipse-ee4j/angus-activation</url>

    <properties>
        <spec.version>2.1</spec.version>
        <spec.title>Jakarta Activation Specification</spec.title>
        <activation-api.version>2.1.2</activation-api.version>
        <angus-activation.version>${project.version}</angus-activation.version>
        <graal.sdk.version>22.3.0</graal.sdk.version>

        <angus-activation.javadoc.source>11</angus-activation.javadoc.source>
        <angus-activation.javadoc.title>Angus Activation API documentation</angus-activation.javadoc.title>
        <angus-activation.javadoc.header><![CDATA[<br>Angus Activation v${angus-activation.version}]]></angus-activation.javadoc.header>
        <angus-activation.javadoc.bottom><![CDATA[
Comments to: <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
Copyright &#169; 2019, ${current.year} Eclipse Foundation. All rights reserved.]]></angus-activation.javadoc.bottom>

        <copyright.exclude>etc/copyright-exclude</copyright.exclude>
        <copyright.ignoreyear>false</copyright.ignoreyear>
        <copyright.scmonly>true</copyright.scmonly>
        <copyright.update>false</copyright.update>
        <spotbugs.skip>false</spotbugs.skip>
        <spotbugs.threshold>Low</spotbugs.threshold>
        <spotbugs.exclude>etc/spotbugs-exclude.xml</spotbugs.exclude>
        <spotbugs.version>*******</spotbugs.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <scm>
        <connection>scm:git:ssh://**************/eclipse/angus-activation.git</connection>
        <developerConnection>scm:git:ssh://**************/eclipse/angus-activation.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/angus-activation</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/angus-activation/issues/</url>
    </issueManagement>

    <licenses>
        <license>
            <name>EDL 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>shannon</id>
            <name>Bill Shannon</name>
            <email><EMAIL></email>
            <organization>Oracle</organization>
            <roles>
                <role>lead</role>
            </roles>
        </developer>
    </developers>

    <!-- following to enable use of "mvn site:stage" -->
    <distributionManagement>
        <site>
            <id>oracle.com</id>
            <url>file:/tmp</url> <!-- not used -->
        </site>
    </distributionManagement>

    <modules>
        <module>activation-registry</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${activation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.angus</groupId>
                <artifactId>angus-activation</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.graalvm.sdk</groupId>
                <artifactId>graal-sdk</artifactId>
                <version>${graal.sdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>

        <defaultGoal>install</defaultGoal>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.8</version>
                    <configuration>
                        <instructions>
                            <_noextraheaders>true</_noextraheaders>
                        </instructions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs.version}</version>
                    <configuration>
                        <skip>${spotbugs.skip}</skip>
                        <threshold>${spotbugs.threshold}</threshold>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.1.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.ant</groupId>
                            <artifactId>ant</artifactId>
                            <version>1.10.13</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>3.4.3</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.10</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!--
            Make sure we're using the correct version of maven.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-version</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.6.3,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[11,)</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--
            Use the JDK 9+ compiler but with -source 1.8 for all
            but the module-info.java file.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>9</release>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                    </compilerArgs>
                    <showDeprecation>true</showDeprecation>
                    <createMissingPackageInfoClass>false</createMissingPackageInfoClass>
                </configuration>
                <executions>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>8</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                        <configuration>
                            <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                            <shortRevisionLength>7</shortRevisionLength>
                            <revisionOnScmFailure>false</revisionOnScmFailure>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <niceManifest>true</niceManifest>
                    <instructions>
                        <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
                        <Specification-Title>${spec.title}</Specification-Title>
                        <Specification-Version>${spec.version}</Specification-Version>
                        <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                        <Extension-Name>${project.groupId}</Extension-Name>
                        <Implementation-Title>${project.name}</Implementation-Title>
                        <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                        <Implementation-Build-Id>${scmBranch}-${buildNumber}</Implementation-Build-Id>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <!--
            Tell the source plugin about the sources that may have
            been downloaded by the maven-dependency-plugin.
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>currentyear-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <name>current.year</name>
                            <locale>en,US</locale>
                            <pattern>yyyy</pattern>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source> <!-- for dependencies -->
                                    ${project.build.directory}/generated-sources/sources
                                </source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/../</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <source>${angus-activation.javadoc.source}</source>
                    <author>false</author>
                    <description>${angus-activation.javadoc.title}</description>
                    <doctitle>${angus-activation.javadoc.title}</doctitle>
                    <windowtitle>${angus-activation.javadoc.title}</windowtitle>
                    <splitindex>true</splitindex>
                    <use>true</use>
                    <notimestamp>true</notimestamp>
                    <serialwarn>true</serialwarn>
                    <quiet>true</quiet>
                    <header>${angus-activation.javadoc.header}</header>
                    <bottom>${angus-activation.javadoc.bottom}</bottom>
                    <!-- force the doc-files directory to be copied -->
                    <docfilessubdirs>true</docfilessubdirs>
                    <detectJavaApiLink>false</detectJavaApiLink>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <excludeFile>${copyright.exclude}</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>${copyright.scmonly}</scmOnly>
                    <!-- for use with repair -->
                    <update>${copyright.update}</update>
                    <!-- check that year is correct -->
                    <ignoreYear>${copyright.ignoreyear}</ignoreYear>
                    <quiet>false</quiet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <excludeFilterFile>${spotbugs.exclude}</excludeFilterFile>
                    <failThreshold>High</failThreshold>
                </configuration>
            </plugin>
        </plugins>

    </build>

    <profiles>
        <!--
            This profile contains modules that should only be built
            but not installed or deployed.
        -->
        <profile>
            <id>build-only</id>
            <modules>
                <module>demo</module>
                <module>docs</module>
            </modules>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--
            This profile is used for deploying a Jakarta Activation
            final release.

            Activating this profile manually for deployment causes
            the above profile to be deactivated, which works around
            an apparent bug in maven that prevents me from manually
            deactivating a profile.  This profile purposely has none
            of the modules I don't want to be deployed.
        -->
        <profile>
            <id>oss-release</id>
        </profile>
    </profiles>

</project>
