<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.ai</groupId>
  <artifactId>spring-ai-autoconfigure-model-ollama</artifactId>
  <version>1.0.0</version>
  <name>Spring AI Ollama Auto Configuration</name>
  <description>Spring AI Ollama Auto Configuration</description>
  <url>https://github.com/spring-projects/spring-ai</url>
  <organization>
    <name>VMware Inc.</name>
    <url>https://spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mpollack</id>
      <name>Mark Pollack</name>
      <email>mpollack at vmware.com</email>
      <organization>VMware</organization>
      <organizationUrl>http://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>tzolov</id>
      <name>Christian Tzolov</name>
      <email>christian tzolov at broadcom.com</email>
      <organization>Broadcom</organization>
      <organizationUrl>http://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>git://github.com/spring-projects/spring-ai.git</connection>
    <developerConnection>**************:spring-projects/spring-ai.git</developerConnection>
    <url>https://github.com/spring-projects/spring-ai</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-ollama</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-autoconfigure-model-tool</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-autoconfigure-model-chat-observation</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-autoconfigure-model-embedding-observation</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <version>3.4.5</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <version>3.4.5</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure-processor</artifactId>
      <version>3.4.5</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
