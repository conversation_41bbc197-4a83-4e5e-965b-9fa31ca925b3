<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023-2024 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.ai</groupId>
  <artifactId>spring-ai-openai</artifactId>
  <version>1.0.0</version>
  <name>Spring AI Model - OpenAI</name>
  <description>OpenAI models support</description>
  <url>https://github.com/spring-projects/spring-ai</url>
  <organization>
    <name>VMware Inc.</name>
    <url>https://spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mpollack</id>
      <name>Mark Pollack</name>
      <email>mpollack at vmware.com</email>
      <organization>VMware</organization>
      <organizationUrl>http://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>tzolov</id>
      <name>Christian Tzolov</name>
      <email>christian tzolov at broadcom.com</email>
      <organization>Broadcom</organization>
      <organizationUrl>http://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>git://github.com/spring-projects/spring-ai.git</connection>
    <developerConnection>**************:spring-projects/spring-ai.git</developerConnection>
    <url>https://github.com/spring-projects/spring-ai</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-model</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-retry</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.victools</groupId>
      <artifactId>jsonschema-generator</artifactId>
      <version>4.37.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.victools</groupId>
      <artifactId>jsonschema-module-jackson</artifactId>
      <version>4.37.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
      <version>6.2.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.17</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
