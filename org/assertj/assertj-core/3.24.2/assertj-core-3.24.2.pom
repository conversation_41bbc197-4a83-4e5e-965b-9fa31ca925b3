<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-parent</artifactId>
    <version>3.24.2</version>
    <relativePath>../assertj-parent</relativePath>
  </parent>

  <artifactId>assertj-core</artifactId>

  <name>AssertJ Core</name>
  <description>Rich and fluent assertions for testing in Java</description>
  <url>${project.organization.url}#${project.artifactId}</url>

  <properties>
    <argLine>
      -Dfile.encoding=${project.build.sourceEncoding}
      -Dnet.bytebuddy.experimental=true
      --add-opens=java.base/java.lang=ALL-UNNAMED
      --add-opens=java.base/java.math=ALL-UNNAMED
      --add-opens=java.base/java.util=ALL-UNNAMED
      --add-opens=java.base/sun.nio.fs=ALL-UNNAMED
    </argLine>
    <javadocAdditionalOptions>-html5 --allow-script-in-comments</javadocAdditionalOptions>
    <!-- Dependency versions -->
    <byte-buddy.version>1.12.21</byte-buddy.version>
    <hamcrest.version>2.2</hamcrest.version>
    <!-- Plugin versions -->
    <cdg.pitest.version>1.0.3</cdg.pitest.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency> <!-- Included by mockito-core -->
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency> <!-- Included by hibernate-core -->
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>3.5.0.Final</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <!-- Compile -->
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
    </dependency>
    <!-- Provided -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
      <exclusions>
        <!-- This has to be excluded because it's subjecting the code to the incorrect version of certain hamcrest packages. -->
        <exclusion>
          <groupId>org.hamcrest</groupId>
          <artifactId>hamcrest-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.opentest4j</groupId>
      <artifactId>opentest4j</artifactId>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <!-- Test -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.14.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>31.1-jre</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.ws.rs</groupId>
      <artifactId>jakarta.ws.rs-api</artifactId>
      <version>3.1.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>nl.jqno.equalsverifier</groupId>
      <artifactId>equalsverifier</artifactId>
      <version>3.12.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>6.1.6.Final</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>jakarta.inject</groupId>
          <artifactId>jakarta.inject-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit-pioneer</groupId>
      <artifactId>junit-pioneer</artifactId>
      <version>1.9.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-testkit</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.assertj</groupId>
          <artifactId>assertj-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- required to run JUnit4 tests in eclipse without to explicitly select JUnit 4 runner ... -->
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>5.3.24</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>yuicompressor-maven-plugin</artifactId>
          <version>1.5.1</version>
        </plugin>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build
          itself. -->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <versionRange>[2.6,)</versionRange>
                    <goals>
                      <goal>format</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.github.siom79.japicmp</groupId>
          <artifactId>japicmp-maven-plugin</artifactId>
          <configuration>
            <dependencies>
              <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
              </dependency>
              <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest</artifactId>
                <version>${hamcrest.version}</version>
              </dependency>
              <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-jupiter.version}</version>
              </dependency>
              <dependency>
                <groupId>org.opentest4j</groupId>
                <artifactId>opentest4j</artifactId>
                <version>${opentest4j.version}</version>
              </dependency>
            </dependencies>
            <parameter>
              <excludes>
                <exclude>org.assertj.core.internal</exclude>
              </excludes>
              <overrideCompatibilityChangeParameters>
                <!-- Unlikely that AssertJ users will encounter an IncompatibleClassChangeError (ref: JLS 13.5.6) -->
                <overrideCompatibilityChangeParameter>
                  <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                  <binaryCompatible>true</binaryCompatible>
                  <sourceCompatible>true</sourceCompatible>
                </overrideCompatibilityChangeParameter>
              </overrideCompatibilityChangeParameters>
            </parameter>
            <skipXmlReport>true</skipXmlReport>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java-and-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <includes>
                    <include>net.bytebuddy:byte-buddy</include>
                  </includes>
                  <excludes>
                    <exclude>org.assertj:assertj-core</exclude>
                    <exclude>org.hamcrest:hamcrest-core</exclude>
                    <exclude>*:*:*:jar:compile</exclude>
                  </excludes>
                </bannedDependencies>
                <dependencyConvergence />
                <requireJavaVersion>
                  <version>[17,)</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
        </configuration>
        <executions>
          <execution>
            <id>jdk9</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <release>9</release>
              <compileSourceRoots>
                <compileSourceRoot>${project.basedir}/src/main/java9</compileSourceRoot>
              </compileSourceRoots>
              <multiReleaseOutput>true</multiReleaseOutput>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M7</version>
        <configuration>
          <trimStackTrace>false</trimStackTrace>
          <excludes>
            <exclude>org/assertj/core/internal/objects/Objects_assertHasOnlyFields_Test*</exclude>
          </excludes>
          <runOrder>random</runOrder>

          <!--
            These blocks are needed to show @DisplayName and @ParameterizedTest
            in reports with the provided names, as well as with console output.
          -->
          <consoleOutputReporter implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5ConsoleOutputReporter">
            <!-- Set to true to suppress log output being included in Surefire output -->
            <disable>false</disable>
          </consoleOutputReporter>

          <statelessTestsetInfoReporter implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5StatelessTestsetInfoReporter">
            <disable>false</disable>
            <usePhrasedFileName>false</usePhrasedFileName>
            <usePhrasedClassNameInRunning>true</usePhrasedClassNameInRunning>
            <usePhrasedClassNameInTestCaseSummary>true</usePhrasedClassNameInTestCaseSummary>
          </statelessTestsetInfoReporter>

          <statelessTestsetReporter implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
            <disable>false</disable>
            <usePhrasedFileName>false</usePhrasedFileName>
            <usePhrasedTestSuiteClassName>true</usePhrasedTestSuiteClassName>
            <usePhrasedTestCaseClassName>true</usePhrasedTestCaseClassName>
            <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
          </statelessTestsetReporter>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <rules>
            <rule implementation="org.jacoco.maven.RuleConfiguration">
              <element>BUNDLE</element>
              <limits>
                <limit implementation="org.jacoco.report.check.Limit">
                  <counter>CLASS</counter>
                  <value>COVEREDRATIO</value>
                  <minimum>0.98</minimum>
                </limit>
              </limits>
            </rule>
          </rules>
        </configuration>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>jar</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <bnd><![CDATA[
                Import-Package:\
                  net.bytebuddy.*,\
                  !org.assertj.core.*,\
                  *;resolution:="optional"
                Multi-Release: true
                -exportcontents: \
                  !org.assertj.core.internal.*,\
                  org.assertj.core.*
                -noclassforname: true
                -noextraheaders: true
                -snapshot: SNAPSHOT
                -removeheaders: Private-Package
                -fixupmessages: \
                  "Classes found in the wrong directory...";is:=ignore
              ]]></bnd>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>yuicompressor-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase> <!-- invoked by maven-javadoc-plugin -->
            <goals>
              <goal>compress</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sourceDirectory>src/main/javadoc</sourceDirectory>
          <outputDirectory>${project.build.directory}/javadoc-stylesheet</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <!-- Explicitly set Javadoc locale to English -->
          <locale>en_US</locale>
          <!-- needed to fix javadoc on java 11+ https://github.com/assertj/assertj/issues/1403 -->
          <source>8</source>
          <!-- (1) CSS file location -->
          <stylesheetfile>${project.build.directory}/javadoc-stylesheet/assertj-javadoc-min.css</stylesheetfile>
          <!-- init Highlight -->
          <additionalOptions>${javadocAdditionalOptions}</additionalOptions>
          <nohelp>true</nohelp>
          <bottom><![CDATA[
            Copyright &#169; {currentYear} {organizationName}. All rights reserved.
            <link rel="stylesheet" href="./assertj-javadoc.css"">
            <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
            <script>hljs.highlightAll();</script>
        ]]></bottom>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.pitest</groupId>
        <artifactId>pitest-maven</artifactId>
        <version>1.10.4</version>
        <dependencies>
          <dependency>
            <groupId>org.pitest</groupId>
            <artifactId>pitest-junit5-plugin</artifactId>
            <version>1.1.1</version>
          </dependency>
          <dependency>
            <groupId>com.groupcdg</groupId>
            <artifactId>pitest-git-plugin</artifactId>
            <version>${cdg.pitest.version}</version>
          </dependency>
        </dependencies>
        <configuration>
          <threads>3</threads>
          <timestampedReports>false</timestampedReports>
          <failWhenNoMutations>false</failWhenNoMutations>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>pitest</id>
      <build>
        <pluginManagement>
          <plugins>
            <!-- provides the pitest-git:aggregate goal to aggregate json files to be stored as artifact when accepting PRs from untrusted forks
     refers to pitest-receive-pr.yml and pitest-updated-pr.yml for details.
-->
            <plugin>
              <groupId>com.groupcdg</groupId>
              <artifactId>pitest-git-maven-plugin</artifactId>
              <version>${cdg.pitest.version}</version>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <groupId>org.pitest</groupId>
            <artifactId>pitest-maven</artifactId>
            <executions>
              <execution>
                <id>pitest</id>
                <phase>test-compile</phase>
                <goals>
                  <goal>mutationCoverage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>japicmp-branch</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>com.github.siom79.japicmp</groupId>
              <artifactId>japicmp-maven-plugin</artifactId>
              <configuration>
                <oldVersion>
                  <file>
                    <path>${japicmp.oldVersion.basedir}/target/${project.build.finalName}.${project.packaging}</path>
                  </file>
                </oldVersion>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>

</project>
