<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd ">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.assertj</groupId>
  <artifactId>assertj-build</artifactId>
  <version>3.24.2</version>
  <packaging>pom</packaging>

  <name>AssertJ Build</name>
  <description>AssertJ Build</description>
  <url>${project.organization.url}</url>
  <organization>
    <name>AssertJ</name>
    <url>https://assertj.github.io/doc/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>joel-costigliola</id>
      <name>Joel Costigliola</name>
      <email>joel.costigliola at gmail.com</email>
      <roles>
        <role>Owner</role>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>scordio</id>
      <name>Stefano Cordio</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>PascalSchumacher</id>
      <name>Pascal Schumacher</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>epeee</id>
      <name>Erhard Pointl</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>croesch</id>
      <name>Christian Rösch</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>VanRoy</id>
      <name>Julien Roy</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>regis1512</id>
      <name>Régis Pouiller</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>fbiville</id>
      <name>Florent Biville</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>Patouche</id>
      <name>Patrick Allain</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <modules>
    <module>assertj-bom</module>
    <module>assertj-parent</module>
    <module>assertj-core</module>
    <module>assertj-guava</module>
    <module>assertj-tests</module>
  </modules>

  <scm>
    <developerConnection>scm:git:https://github.com/assertj/assertj.git</developerConnection>
    <connection>scm:git:https://github.com/assertj/assertj.git</connection>
    <url>https://github.com/assertj/assertj</url>
    <tag>assertj-build-3.24.2</tag>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/assertj/assertj/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <!-- Plugin versions -->
    <license-maven-plugin.version>4.1</license-maven-plugin.version>
    <maven-enforcer-plugin.version>3.1.0</maven-enforcer-plugin.version>
    <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
    <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
    <nexus-staging-maven-plugin.version>1.6.13</nexus-staging-maven-plugin.version>
    <sonar-maven-plugin.version>3.9.1.2184</sonar-maven-plugin.version>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>com.mycila</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${license-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${maven-gpg-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <useReleaseProfile>false</useReleaseProfile>
            <releaseProfiles>release</releaseProfiles>
            <goals>deploy</goals>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.sonarsource.scanner.maven</groupId>
          <artifactId>sonar-maven-plugin</artifactId>
          <version>${sonar-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>${nexus-staging-maven-plugin.version}</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>true</autoReleaseAfterClose>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <configuration>
          <inlineHeader><![CDATA[
Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
the License. You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.

Copyright ${inceptionYear}-${currentYear} the original author or authors.
          ]]></inlineHeader>
          <properties>
            <!-- Values to be substituted in template -->
            <inceptionYear>2012</inceptionYear>
            <currentYear>2023</currentYear>
          </properties>
          <strictCheck>true</strictCheck>
          <includes>
            <include>src/**/*.java</include>
          </includes>
          <excludes>
            <exclude>src/ide-support/**/*.*</exclude>
          </excludes>
          <mapping>
            <java>SLASHSTAR_STYLE</java>
          </mapping>
          <errorMessage>Some files do not have the expected license header. Run license:format to update them.</errorMessage>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>[3.5.0,)</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <inherited>false</inherited>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
