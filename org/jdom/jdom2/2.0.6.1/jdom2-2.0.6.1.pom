<project>
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.jdom</groupId>
	<artifactId>jdom2</artifactId>
	<packaging>jar</packaging>
	
	<name>JDOM</name>
	<version>*******</version>
	
	<description>
		A complete, Java-based solution for accessing, manipulating, 
		and outputting XML data
	</description>
	<url>http://www.jdom.org</url>

    <organization>
        <name>JDOM</name>
        <url>http://www.jdom.org</url>
    </organization>

    <mailingLists>
        <mailingList>
            <name>JDOM-interest Mailing List</name>
            <post><EMAIL></post>
            <archive>http://jdom.markmail.org/</archive>
        </mailingList>
    </mailingLists>

	<licenses>
		<license>
			<name>Similar to Apache License but with the acknowledgment clause removed</name>
			<url>https://raw.github.com/hunterhacker/jdom/master/LICENSE.txt</url>
			<distribution>repo</distribution>
			<comments xml:space="preserve"><![CDATA[
			
/*-- 

 Copyright (C) 2000-2012 Jason <PERSON> & <PERSON>.
 All rights reserved.
 
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 
 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions, and the following disclaimer.
 
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions, and the disclaimer that follows 
    these conditions in the documentation and/or other materials 
    provided with the distribution.

 3. The name "JDOM" must not be used to endorse or promote products
    derived from this software without prior written permission.  For
    written permission, please contact <request_AT_jdom_DOT_org>.
 
 4. Products derived from this software may not be called "JDOM", nor
    may "JDOM" appear in their name, without prior written permission
    from the JDOM Project Management <request_AT_jdom_DOT_org>.
 
 In addition, we request (but do not require) that you include in the 
 end-user documentation provided with the redistribution and/or in the 
 software itself an acknowledgement equivalent to the following:
     "This product includes software developed by the
      JDOM Project (http://www.jdom.org/)."
 Alternatively, the acknowledgment may be graphical using the logos 
 available at http://www.jdom.org/images/logos.

 THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED.  IN NO EVENT SHALL THE JDOM AUTHORS OR THE PROJECT
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 SUCH DAMAGE.

 This software consists of voluntary contributions made by many 
 individuals on behalf of the JDOM Project and was originally 
 created by Jason Hunter <jhunter_AT_jdom_DOT_org> and
 Brett McLaughlin <brett_AT_jdom_DOT_org>.  For more information
 on the JDOM Project, please see <http://www.jdom.org/>. 

 */



			]]></comments>
		</license>
	</licenses>

	<scm>
		<url>**************:/hunterhacker/jdom</url>
		<connection>scm:git:**************:hunterhacker/jdom</connection>
		<developerConnection>scm:git:**************:hunterhacker/jdom</developerConnection>
	</scm>

	<developers>
		<developer>
			<id>hunterhacker</id>
			<name>Jason Hunter</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>rolfl</id>
			<name>Rolf Lear</name>
			<email><EMAIL></email>
		</developer>
	</developers>
	
	<dependencies>
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<version>1.2.0</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.11.0</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>xalan</groupId>
			<artifactId>xalan</artifactId>
			<version>2.7.2</version>
			<optional>true</optional>
		</dependency>

	</dependencies>
	
	<properties>
		<jdk.version>1.5</jdk.version>
	</properties>
</project>