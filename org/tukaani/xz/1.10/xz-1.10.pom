<?xml version="1.0" encoding="UTF-8"?>
<!-- SPDX-License-Identifier: 0BSD -->
<!-- SPDX-FileCopyrightText: The XZ for Java authors and contributors -->
<!-- SPDX-FileContributor: <PERSON><PERSON> <<EMAIL>> -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <groupId>org.tukaani</groupId>
    <artifactId>xz</artifactId>
    <version>1.10</version>
    <packaging>jar</packaging>

    <name>XZ for Java</name>
    <description>XZ data compression</description>
    <url>https://tukaani.org/xz/java.html</url>

    <licenses>
        <license>
            <name>0BSD</name>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/tukaani-project/xz-java</url>
        <connection>scm:git:https://github.com/tukaani-project/xz-java</connection>
    </scm>

    <developers>
        <developer>
            <name>Lasse Collin</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <!-- According to Maven docs, it's good to only list those people
                 as <developers> that should be contacted if someone wants
                 to talk with an upstream developer. Thus, Igor Pavlov is
                 marked as a <contributor> even though XZ for Java simply
                 couldn't exist without Igor Pavlov's code. -->
            <name>Igor Pavlov</name>
            <url>https://7-zip.org/</url>
        </contributor>

        <contributor>
            <name>Brett Okken</name>
            <email><EMAIL></email>
        </contributor>
    </contributors>

</project>
