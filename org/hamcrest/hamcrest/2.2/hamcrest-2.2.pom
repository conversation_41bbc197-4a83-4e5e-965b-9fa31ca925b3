<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.hamcrest</groupId>
  <artifactId>hamcrest</artifactId>
  <version>2.2</version>
  <name>Hamcrest</name>
  <description>Core API and libraries of hamcrest matcher framework.</description>
  <url>http://hamcrest.org/JavaHamcrest/</url>
  <licenses>
    <license>
      <name>BSD License 3</name>
      <url>http://opensource.org/licenses/BSD-3-Clause</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>j<PERSON><PERSON><PERSON></id>
      <name><PERSON></name>
    </developer>
    <developer>
      <id>npryce</id>
      <name><PERSON></name>
    </developer>
    <developer>
      <id>sf105</id>
      <name><PERSON></name>
    </developer>
  </developers>
  <scm>
    <connection>**************:hamcrest/JavaHamcrest.git</connection>
    <url>https://github.com/hamcrest/JavaHamcrest</url>
  </scm>
</project>
