package com.sinitek.mind.core.workflow.util;

import com.sinitek.mind.support.file.model.ReadFileResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileUtil测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Slf4j
@SpringBootTest
public class FileUtilTest {

    @Autowired
    private FileUtil fileUtil;

    @Test
    public void testDetectFileEncoding() {
        // 测试UTF-8编码检测
        String testText = "Hello World 你好世界";
        byte[] utf8Bytes = testText.getBytes(StandardCharsets.UTF_8);
        
        String detectedEncoding = FileUtil.detectFileEncoding(utf8Bytes);
        log.info("Detected encoding: {}", detectedEncoding);
        
        assertNotNull(detectedEncoding);
        assertTrue(detectedEncoding.toLowerCase().contains("utf"));
    }

    @Test
    public void testDetectFileEncodingWithEmptyBuffer() {
        String encoding = FileUtil.detectFileEncoding(null);
        assertEquals("utf-8", encoding);
        
        encoding = FileUtil.detectFileEncoding(new byte[0]);
        assertEquals("utf-8", encoding);
    }

    @Test
    public void testParseFileExtensionFromUrl() {
        assertEquals("pdf", FileUtil.parseFileExtensionFromUrl("http://example.com/file.pdf"));
        assertEquals("txt", FileUtil.parseFileExtensionFromUrl("http://example.com/file.txt?param=value"));
        assertEquals("", FileUtil.parseFileExtensionFromUrl("http://example.com/file"));
        assertEquals("", FileUtil.parseFileExtensionFromUrl(""));
        assertEquals("", FileUtil.parseFileExtensionFromUrl(null));
    }

    @Test
    public void testAddAndGetRawTextBuffer() {
        String sourceId = "test_source_" + System.currentTimeMillis();
        String sourceName = "test_file.txt";
        String text = "This is a test content for raw text buffer.";
        Date expiredTime = new Date(System.currentTimeMillis() + 3600000); // 1小时后过期

        // 测试添加原始文本缓冲
        CompletableFuture<String> addResult = fileUtil.addRawTextBuffer(sourceId, sourceName, text, expiredTime);
        String fileId = addResult.join();
        
        assertNotNull(fileId);
        assertFalse(fileId.isEmpty());
        log.info("Added raw text buffer with fileId: {}", fileId);

        // 测试获取原始文本缓冲
        FileUtil.RawTextBufferResult result = fileUtil.getRawTextBuffer(sourceId);
        
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(sourceName, result.getSourceName());
        log.info("Retrieved raw text buffer: {}", result.getText());
    }

    @Test
    public void testReadRawContentByFileBuffer() {
        // 测试文本文件解析
        String testContent = "This is a test text file content.";
        byte[] buffer = testContent.getBytes(StandardCharsets.UTF_8);
        
        CompletableFuture<ReadFileResponse> result = fileUtil.readRawContentByFileBuffer(
            "test_team", "test_tmb", "txt", buffer, "utf-8", false, false
        );
        
        ReadFileResponse response = result.join();
        assertNotNull(response);
        assertNotNull(response.getRawText());
        log.info("Parsed text content: {}", response.getRawText());
    }

    @Test
    public void testFormatResponseObject() {
        String filename = "test.txt";
        String url = "http://example.com/test.txt";
        String content = "This is test content";
        
        FileUtil.FileResponseObject response = FileUtil.formatResponseObject(filename, url, content);
        
        assertNotNull(response);
        assertEquals(filename, response.getFilename());
        assertEquals(url, response.getUrl());
        assertTrue(response.getText().contains(content));
        assertTrue(response.getNodeResponsePreviewText().contains(content));
    }
}
