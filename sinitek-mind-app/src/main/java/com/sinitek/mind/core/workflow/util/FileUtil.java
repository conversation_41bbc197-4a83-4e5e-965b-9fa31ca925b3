package com.sinitek.mind.core.workflow.util;

import com.sinitek.mind.core.chat.enumerate.ChatFileType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.support.file.constant.FileConstant;
import com.sinitek.mind.support.file.model.ReadFileResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.mozilla.universalchardet.UniversalDetector;

/**
 * 文件工具类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Slf4j
@Component
public class FileUtil {

    /**
     * 从URL解析文件扩展名
     * 
     * @param url URL字符串
     * @return 文件扩展名（小写）
     */
    public static String parseFileExtensionFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        
        // 移除查询参数
        String urlWithoutQuery = url.split("\\?")[0];
        
        // 获取文件名
        String[] pathParts = urlWithoutQuery.split("/");
        String fileName = pathParts.length > 0 ? pathParts[pathParts.length - 1] : "";
        
        // 获取文件扩展名
        String[] nameParts = fileName.split("\\.");
        String extension = nameParts.length > 1 ? nameParts[nameParts.length - 1] : "";
        
        return extension.toLowerCase();
    }

    /**
     * 解析URL到文件类型
     * 
     * @param url URL字符串
     * @return 用户聊天项值类型的文件信息，如果无法解析则返回null
     */
    public static ChatItemValueItemFileInfo parseUrlToFileType(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 处理base64图片
        if (url.startsWith("data:")) {
            Pattern pattern = Pattern.compile("^data:([^;]+);base64,");
            Matcher matcher = pattern.matcher(url);
            if (!matcher.find()) {
                return null;
            }

            String mimeType = matcher.group(1).toLowerCase();
            if (!mimeType.startsWith("image/")) {
                return null;
            }

            String extension = mimeType.split("/")[1];
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    "image." + extension,
                    url
            );
        }

        try {
            URL parseUrl = new URL(url);
            
            // 从URL获取文件名
            String filename = null;
            String queryString = parseUrl.getQuery();
            if (StringUtils.isNotBlank(queryString)) {
                // 尝试从查询参数中获取filename
                Pattern filenamePattern = Pattern.compile("[?&]filename=([^&]*)");
                Matcher filenameMatcher = filenamePattern.matcher("?" + queryString);
                if (filenameMatcher.find()) {
                    filename = URLDecoder.decode(filenameMatcher.group(1), StandardCharsets.UTF_8);
                }
            }
            
            if (StringUtils.isBlank(filename)) {
                String[] pathParts = parseUrl.getPath().split("/");
                filename = pathParts.length > 0 ? pathParts[pathParts.length - 1] : "";
            }
            
            String extension = "";
            if (StringUtils.isNotBlank(filename)) {
                String[] nameParts = filename.split("\\.");
                extension = nameParts.length > 1 ? nameParts[nameParts.length - 1].toLowerCase() : "";
            }

            // 如果是文档类型，返回文件类型，否则当作图片处理
            if (StringUtils.isNotBlank(extension) && FileConstant.DOCUMENT_FILE_TYPES.contains(extension)) {
                return new ChatItemValueItemFileInfo(
                        ChatFileType.FILE.getValue(),
                        StringUtils.isNotBlank(filename) ? filename : "null",
                        url
                );
            }

            // 默认当作图片类型处理
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    StringUtils.isNotBlank(filename) ? filename : "null.png",
                    url
            );
                    
        } catch (MalformedURLException e) {
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    "invalid.png",
                    url
            );
        }
    }

    /**
     * 格式化响应对象
     * 
     * @param filename 文件名
     * @param url URL
     * @param content 内容
     * @return 格式化后的响应对象
     */
    public static FileResponseObject formatResponseObject(String filename, String url, String content) {
        String text = String.format("File: %s\n<Content>\n%s\n</Content>", filename, content);
        
        String previewContent = content.length() > 100 ? 
            content.substring(0, 100) + "......" : content;
        String nodeResponsePreviewText = String.format("File: %s\n<Content>\n%s\n</Content>", 
            filename, previewContent);
        
        return FileResponseObject.builder()
                .filename(filename)
                .url(url)
                .text(text)
                .nodeResponsePreviewText(nodeResponsePreviewText)
                .build();
    }



    /**
     * 检测文件编码
     * 
     * @param buffer 文件字节数组
     * @return 检测到的编码，如果无法检测则返回null
     */
    public static String detectFileEncoding(byte[] buffer) {
        // TODO: 实现文件编码检测逻辑
        // 原TypeScript使用detect库，Java中可以使用juniversalchardet或其他编码检测库
        log.warn("detectFileEncoding not implemented yet, returning UTF-8 as default");
        return "utf-8";
    }

    /**
     * 从链接获取文件内容
     * 
     * @param urls URL列表
     * @param requestOrigin 请求来源
     * @param maxFiles 最大文件数
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @param customPdfParse 是否使用自定义PDF解析
     * @return 文件内容结果
     */
    public static CompletableFuture<FileContentResult> getFileContentFromLinks(
            List<String> urls, 
            String requestOrigin, 
            int maxFiles, 
            String teamId, 
            String tmbId, 
            boolean customPdfParse) {
        
        return CompletableFuture.supplyAsync(() -> {
            // 过滤和处理URL列表
            List<String> parseUrlList = urls.stream()
                    // 移除无效的URL
                    .filter(url -> {
                        if (StringUtils.isBlank(url)) {
                            return false;
                        }
                        
                        // 检查相对路径
                        return FileConstant.VALID_URL_PREFIXES.stream()
                                .anyMatch(url::startsWith);
                    })
                    // 只获取文档类型文件
                    .filter(url -> {
                        ChatItemValueItemFileInfo fileInfo = parseUrlToFileType(url);
                        return fileInfo != null && ChatFileType.FILE.getValue().equals(fileInfo.getType());
                    })
                    .map(url -> {
                        try {
                            // 检查是否为系统上传文件
                            if (url.startsWith("/") || (StringUtils.isNotBlank(requestOrigin) && url.startsWith(requestOrigin))) {
                                // 移除origin（直接进行内网请求）
                                if (StringUtils.isNotBlank(requestOrigin) && url.startsWith(requestOrigin)) {
                                    url = url.replace(requestOrigin, "");
                                }
                            }
                            return url;
                        } catch (Exception e) {
                            log.warn("Parse url error: {}", e.getMessage());
                            return "";
                        }
                    })
                    .filter(StringUtils::isNotBlank)
                    .limit(maxFiles)
                    .collect(Collectors.toList());

            // 并行处理文件读取
            List<CompletableFuture<FileResponseObject>> futures = parseUrlList.stream()
                    .map(url -> processFileUrl(url, teamId, tmbId, customPdfParse))
                    .collect(Collectors.toList());

            // 等待所有任务完成
            List<FileResponseObject> readFilesResult = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 合并所有文件内容
            String text = readFilesResult.stream()
                    .map(FileResponseObject::getText)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining("\n******\n"));

            return FileContentResult.builder()
                    .text(text)
                    .readFilesResult(readFilesResult)
                    .build();
        });
    }

    /**
     * 处理单个文件URL
     */
    private static CompletableFuture<FileResponseObject> processFileUrl(String url, String teamId, String tmbId, boolean customPdfParse) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 从缓冲获取
                RawTextBufferResult fileBuffer = getRawTextBuffer(url);
                if (fileBuffer != null) {
                    return formatResponseObject(
                            StringUtils.isNotBlank(fileBuffer.getSourceName()) ? fileBuffer.getSourceName() : url,
                            url,
                            fileBuffer.getText()
                    );
                }

                // TODO: 实现HTTP请求获取文件数据
                // 原TypeScript使用axios.get获取文件，Java中可以使用RestTemplate或WebClient
                log.warn("HTTP file fetching not implemented yet");
                return formatResponseObject("", url, "TODO: 实现HTTP文件获取功能");

            } catch (Exception e) {
                log.error("Error processing file URL: {}", url, e);
                return formatResponseObject("", url, "Load file error: " + e.getMessage());
            }
        });
    }

    /**
     * 获取原始文本缓冲
     * 
     * @param sourceId 源ID
     * @return 原始文本缓冲结果
     */
    public static RawTextBufferResult getRawTextBuffer(String sourceId) {
        // TODO: 实现从GridFS获取原始文本缓冲的逻辑
        // 需要实现retryFn、MongoRawTextBufferSchema.findOne、gridFsStream2Buffer等功能
        log.warn("getRawTextBuffer not implemented yet");
        return null;
    }

    /**
     * 从文件缓冲读取原始内容
     * 
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @param extension 文件扩展名
     * @param buffer 文件缓冲
     * @param encoding 编码
     * @param customPdfParse 是否使用自定义PDF解析
     * @param getFormatText 是否获取格式化文本
     * @return 读取文件响应
     */
    public static CompletableFuture<ReadFileResponse> readRawContentByFileBuffer(
            String teamId, String tmbId, String extension, byte[] buffer, 
            String encoding, boolean customPdfParse, boolean getFormatText) {
        
        return CompletableFuture.supplyAsync(() -> {
            // TODO: 实现文件内容读取逻辑
            // 包括systemParse和parsePdfFromCustomService功能
            log.warn("readRawContentByFileBuffer not implemented yet");
            return ReadFileResponse.builder()
                    .rawText("TODO: 实现文件内容读取")
                    .build();
        });
    }

    /**
     * 添加原始文本缓冲
     * 
     * @param sourceId 源ID
     * @param sourceName 源名称
     * @param text 文本内容
     * @param expiredTime 过期时间
     * @return 操作结果
     */
    public static CompletableFuture<String> addRawTextBuffer(String sourceId, String sourceName, String text, Date expiredTime) {
        return CompletableFuture.supplyAsync(() -> {
            // TODO: 实现添加原始文本缓冲到GridFS的逻辑
            log.warn("addRawTextBuffer not implemented yet");
            return "";
        });
    }

    /**
     * 文件响应对象
     * 对应TypeScript中formatResponseObject函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileResponseObject {
        private String filename;
        private String url;
        private String text;
        private String nodeResponsePreviewText;
    }

    /**
     * 文件内容结果
     * 对应TypeScript中getFileContentFromLinks函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileContentResult {
        private String text;
        private List<FileResponseObject> readFilesResult;
    }

    /**
     * 原始文本缓冲结果
     * 对应TypeScript中getRawTextBuffer函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RawTextBufferResult {
        private String text;
        private String sourceName;
    }
}
